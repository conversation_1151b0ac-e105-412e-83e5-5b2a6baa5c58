import React from 'react';
import { render, screen, waitFor } from '@testing-library/react';
import { Provider } from 'react-redux';
import { BrowserRouter } from 'react-router-dom';
import { configureStore } from '@reduxjs/toolkit';
import XeroCallback from '../XeroCallback';
import authSlice from '../../store/slices/authSlice';

// Mock the toast function
jest.mock('@/components/ui/sonner', () => ({
  toast: {
    error: jest.fn(),
    success: jest.fn(),
  },
}));

// Mock the CSS import
jest.mock('@/assets/css/XeroCallback.css', () => ({}));

// Create a mock store
const createMockStore = (initialState = {}) => {
  return configureStore({
    reducer: {
      auth: authSlice,
    },
    preloadedState: {
      auth: {
        isAuthenticated: true,
        user: null,
        token: 'mock-token',
        isConnected: false,
        isLoading: false,
        error: null,
        oauthUrl: null,
        companyInfo: null,
        accessToken: null,
        refreshToken: null,
        ...initialState,
      },
    },
  });
};

// Helper function to render component with providers
const renderWithProviders = (
  component: React.ReactElement,
  { initialState = {}, route = '/xero/callback?code=test-code' } = {}
) => {
  const store = createMockStore(initialState);
  
  // Mock window.location
  delete (window as any).location;
  window.location = { ...window.location, search: new URL(`http://localhost${route}`).search };
  
  return render(
    <Provider store={store}>
      <BrowserRouter>
        {component}
      </BrowserRouter>
    </Provider>
  );
};

describe('XeroCallback Component', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  test('renders loading state initially', () => {
    renderWithProviders(<XeroCallback />);
    
    expect(screen.getByText('Xero Integration')).toBeInTheDocument();
    expect(screen.getByText('Connecting to Xero...')).toBeInTheDocument();
    expect(screen.getByText('Please wait while we complete the connection...')).toBeInTheDocument();
  });

  test('handles missing authorization code', async () => {
    renderWithProviders(<XeroCallback />, { route: '/xero/callback' });
    
    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument();
      expect(screen.getByText('Missing authorization code from Xero')).toBeInTheDocument();
    });
  });

  test('handles OAuth error from URL parameters', async () => {
    renderWithProviders(<XeroCallback />, { 
      route: '/xero/callback?error=access_denied&error_description=User denied access' 
    });
    
    await waitFor(() => {
      expect(screen.getByText('Connection Failed')).toBeInTheDocument();
      expect(screen.getByText('OAuth Error: User denied access')).toBeInTheDocument();
    });
  });

  test('displays return to dashboard button on error', async () => {
    renderWithProviders(<XeroCallback />, { route: '/xero/callback' });
    
    await waitFor(() => {
      expect(screen.getByText('Return to Dashboard')).toBeInTheDocument();
    });
  });

  test('shows proper loading states', () => {
    renderWithProviders(<XeroCallback />);
    
    // Check for loading animation elements
    expect(document.querySelector('.loading-dot')).toBeInTheDocument();
    expect(document.querySelector('.spin-slow')).toBeInTheDocument();
  });
});
