/* eslint-disable @typescript-eslint/no-explicit-any */
/* eslint-disable react-refresh/only-export-components */
// Lazy-loaded components for code splitting and performance optimization
import React, { lazy, Suspense, ComponentType } from "react";
import { LoadingSpinner, PageLoader } from "@/components/common/LoadingSpinner";
import { ErrorBoundary } from "@/components/ErrorBoundary";

// Lazy load main page components
export const LazyIntegration = lazy(() => import("@/pages/Integration"));
export const LazyLogin = lazy(() => import("@/pages/Login"));
export const LazyRegister = lazy(() => import("@/pages/Register"));
export const LazySettings = lazy(() => import("@/pages/Settings"));
export const LazyCompanies = lazy(() => import("@/pages/Companies"));

export const LazySyncLogs = lazy(() => import("@/pages/SyncLogs"));
export const LazyXeroCallback = lazy(() => import("@/components/XeroCallback"));

// Lazy load feature components
export const LazyUserProfile = lazy(() => import("@/components/UserProfile"));
export const LazyNotifications = lazy(
  () => import("@/components/Notifications")
);
export const LazyReports = lazy(() => import("@/components/Reports"));

// Higher-order component for lazy loading with enhanced error handling
export function withLazyLoading<P extends object>(
  Component: ComponentType<P>,
  fallback?: React.ReactNode,
  errorFallback?: React.ReactNode
) {
  const LazyComponent = (props: P) => (
    <ErrorBoundary fallback={errorFallback}>
      <Suspense fallback={fallback || <PageLoader />}>
        <Component {...props} />
      </Suspense>
    </ErrorBoundary>
  );

  LazyComponent.displayName = `withLazyLoading(${
    Component.displayName || Component.name
  })`;

  return LazyComponent;
}

// Preload function for critical components
export const preloadComponent = (componentImport: () => Promise<any>) => {
  // Preload on user interaction or route change
  const link = document.createElement("link");
  link.rel = "prefetch";
  link.as = "script";

  // Trigger the import to start loading
  componentImport().catch(() => {
    // Ignore preload errors
  });
};

// Route-based lazy loading with preloading
// eslint-disable-next-line react-refresh/only-export-components
export const routeComponents = {
  integration: {
    component: LazyIntegration,
    preload: () => import("@/pages/Integration"),
  },
  login: {
    component: LazyLogin,
    preload: () => import("@/pages/Login"),
  },
  register: {
    component: LazyRegister,
    preload: () => import("@/pages/Register"),
  },
  settings: {
    component: LazySettings,
    preload: () => import("@/pages/Settings"),
  },
  companies: {
    component: LazyCompanies,
    preload: () => import("@/pages/Companies"),
  },
  syncLogs: {
    component: LazySyncLogs,
    preload: () => import("@/pages/SyncLogs"),
  },
  xeroCallback: {
    component: LazyXeroCallback,
    preload: () => import("@/components/XeroCallback"),
  },
};

// Preload critical routes
export const preloadCriticalRoutes = () => {
  // Preload integration and login as they are most commonly accessed
  routeComponents.integration.preload();
  routeComponents.login.preload();
};

// Component for handling route-based code splitting
export const LazyRoute: React.FC<{
  component: ComponentType<any>;
  fallback?: React.ReactNode;
  preload?: () => Promise<any>;
  [key: string]: any;
}> = ({ component: Component, fallback, preload, ...props }) => {
  // Preload on hover or focus for better UX
  const handlePreload = () => {
    if (preload) {
      preload().catch(() => {
        // Ignore preload errors
      });
    }
  };

  return (
    <div onMouseEnter={handlePreload} onFocus={handlePreload}>
      <ErrorBoundary>
        <Suspense fallback={fallback || <PageLoader />}>
          <Component {...props} />
        </Suspense>
      </ErrorBoundary>
    </div>
  );
};

// Lazy loading for modals and overlays
export const LazyModal: React.FC<{
  isOpen: boolean;
  component: ComponentType<any>;
  fallback?: React.ReactNode;
  [key: string]: any;
}> = ({ isOpen, component: Component, fallback, ...props }) => {
  if (!isOpen) return null;

  return (
    <ErrorBoundary>
      <Suspense
        fallback={fallback || <LoadingSpinner overlay text="Loading..." />}
      >
        <Component {...props} />
      </Suspense>
    </ErrorBoundary>
  );
};

// Intersection Observer based lazy loading for components
export const LazyOnVisible: React.FC<{
  component: ComponentType<any>;
  fallback?: React.ReactNode;
  rootMargin?: string;
  threshold?: number;
  [key: string]: any;
}> = ({
  component: Component,
  fallback,
  rootMargin = "50px",
  threshold = 0.1,
  ...props
}) => {
  const [isVisible, setIsVisible] = React.useState(false);
  const ref = React.useRef<HTMLDivElement>(null);

  React.useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true);
          observer.disconnect();
        }
      },
      { rootMargin, threshold }
    );

    if (ref.current) {
      observer.observe(ref.current);
    }

    return () => observer.disconnect();
  }, [rootMargin, threshold]);

  return (
    <div ref={ref}>
      {isVisible ? (
        <ErrorBoundary>
          <Suspense fallback={fallback || <LoadingSpinner />}>
            <Component {...props} />
          </Suspense>
        </ErrorBoundary>
      ) : (
        fallback || (
          <div className="h-32 flex items-center justify-center">
            <LoadingSpinner text="Loading component..." />
          </div>
        )
      )}
    </div>
  );
};

export default {
  LazyIntegration,
  LazyLogin,
  LazyRegister,
  LazySettings,
  LazyCompanies,
  LazySyncLogs,
  LazyXeroCallback,
  LazyUserProfile,
  LazyNotifications,
  LazyReports,
  withLazyLoading,
  preloadComponent,
  routeComponents,
  preloadCriticalRoutes,
  LazyRoute,
  LazyModal,
  LazyOnVisible,
};
