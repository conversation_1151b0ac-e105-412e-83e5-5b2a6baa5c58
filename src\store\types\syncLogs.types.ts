// SyncLogs-related TypeScript interfaces and types

export interface SyncLog {
  id: string;
  timestamp: string;
  endpoint: string;
  status: 'success' | 'error' | 'warning';
  duration: string;
  message: string;
  details?: {
    requestId?: string;
    responseCode?: number;
    recordsProcessed?: number;
    errorDetails?: string;
  };
}

export interface SyncLogFilters {
  dateFrom: string;
  dateTo: string;
  endpoint: string;
  status: string;
  search: string;
}

export interface SyncLogsState {
  logs: SyncLog[];
  filteredLogs: SyncLog[];
  selectedLog: SyncLog | null;
  filters: SyncLogFilters;
  isLoading: boolean;
  error: string | null;
}

export interface FetchSyncLogsResponse {
  logs: SyncLog[];
}

export interface RetrySyncOperationParams {
  logId: string;
  endpoint: string;
}

export interface RetrySyncOperationResponse {
  success: boolean;
  newLog?: SyncLog;
  error?: string;
}
