Stack trace:
Frame         Function      Args
0007FFFF9F80  00021006118E (00021028DEE8, 000210272B3E, 000000000000, 0007FFFF8E80) msys-2.0.dll+0x2118E
0007FFFF9F80  0002100469BA (000000000000, 000000000000, 000000000000, 000000000004) msys-2.0.dll+0x69BA
0007FFFF9F80  0002100469F2 (00021028DF99, 0007FFFF9E38, 000000000000, 000000000000) msys-2.0.dll+0x69F2
0007FFFF9F80  00021006A41E (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A41E
0007FFFF9F80  00021006A545 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A545
0001004F94B7  00021006B9A5 (0007FFFF9F90, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2B9A5
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFCAA9E0000 ntdll.dll
7FFCA87D0000 KERNEL32.DLL
7FFCA7DB0000 KERNELBASE.dll
7FFCA88A0000 USER32.dll
7FFCA83A0000 win32u.dll
7FFCA9430000 GDI32.dll
7FFCA83D0000 gdi32full.dll
7FFCA8510000 msvcp_win.dll
7FFCA8650000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFCA8A70000 advapi32.dll
7FFCA8BF0000 msvcrt.dll
7FFCA8FF0000 sechost.dll
7FFCA9EE0000 RPCRT4.dll
7FFCA7160000 CRYPTBASE.DLL
7FFCA8180000 bcryptPrimitives.dll
7FFCA8F00000 IMM32.DLL
