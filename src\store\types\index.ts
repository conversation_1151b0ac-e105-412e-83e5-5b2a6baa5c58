// Central export file for all types

export * from './auth.types';
export * from './companies.types';
export * from './syncLogs.types';
export * from './settings.types';

// Common types
export interface ApiError {
  message: string;
  code?: string;
  details?: any;
}

export interface LoadingState {
  isLoading: boolean;
  error: string | null;
}

// Redux store types
export interface RootState {
  auth: import('./auth.types').AuthState;
  companies: import('./companies.types').CompaniesState;
  syncLogs: import('./syncLogs.types').SyncLogsState;
  settings: import('./settings.types').SettingsState;
}
