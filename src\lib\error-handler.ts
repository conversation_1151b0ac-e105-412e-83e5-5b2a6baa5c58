// Centralized error handling system
import { toast } from '@/components/ui/sonner';

export interface AppError {
  code: string;
  message: string;
  details?: any;
  timestamp: Date;
  stack?: string;
  context?: Record<string, any>;
}

export enum ErrorCode {
  // Network errors
  NETWORK_ERROR = 'NETWORK_ERROR',
  TIMEOUT_ERROR = 'TIMEOUT_ERROR',
  CONNECTION_ERROR = 'CONNECTION_ERROR',

  // Authentication errors
  AUTH_TOKEN_EXPIRED = 'AUTH_TOKEN_EXPIRED',
  AUTH_INVALID_CREDENTIALS = 'AUTH_INVALID_CREDENTIALS',
  AUTH_UNAUTHORIZED = 'AUTH_UNAUTHORIZED',
  AUTH_FORBIDDEN = 'AUTH_FORBIDDEN',

  // API errors
  API_SERVER_ERROR = 'API_SERVER_ERROR',
  API_BAD_REQUEST = 'API_BAD_REQUEST',
  API_NOT_FOUND = 'API_NOT_FOUND',
  API_VALIDATION_ERROR = 'API_VALIDATION_ERROR',

  // Xero integration errors
  XERO_CONNECTION_FAILED = 'XERO_CONNECTION_FAILED',
  XERO_AUTH_FAILED = 'XERO_AUTH_FAILED',
  XERO_API_ERROR = 'XERO_API_ERROR',
  XERO_TOKEN_EXPIRED = 'XERO_TOKEN_EXPIRED',

  // Application errors
  COMPONENT_ERROR = 'COMPONENT_ERROR',
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR',
}

export class AppErrorHandler {
  private static instance: AppErrorHandler;
  private errorLog: AppError[] = [];
  private maxLogSize = 100;

  private constructor() { }

  static getInstance(): AppErrorHandler {
    if (!AppErrorHandler.instance) {
      AppErrorHandler.instance = new AppErrorHandler();
    }
    return AppErrorHandler.instance;
  }

  // Create standardized error object
  createError(
    code: ErrorCode,
    message: string,
    details?: any,
    context?: Record<string, any>
  ): AppError {
    return {
      code,
      message,
      details,
      timestamp: new Date(),
      stack: new Error().stack,
      context,
    };
  }

  // Log error to internal storage
  private logError(error: AppError): void {
    this.errorLog.unshift(error);

    // Keep log size manageable
    if (this.errorLog.length > this.maxLogSize) {
      this.errorLog = this.errorLog.slice(0, this.maxLogSize);
    }

    // Log to console in development
    if (process.env.NODE_ENV === 'development') {
      console.error('AppError:', error);
    }
  }

  // Handle different types of errors
  handleError(error: unknown, context?: Record<string, any>): AppError {
    let appError: AppError;

    if (error instanceof Error) {
      // Determine error code based on error message/type
      const code = this.determineErrorCode(error);
      appError = this.createError(code, error.message, error, context);
    } else if (typeof error === 'string') {
      appError = this.createError(ErrorCode.UNKNOWN_ERROR, error, null, context);
    } else {
      appError = this.createError(
        ErrorCode.UNKNOWN_ERROR,
        'An unknown error occurred',
        error,
        context
      );
    }

    this.logError(appError);
    this.showUserNotification(appError);

    return appError;
  }

  // Determine error code from error object
  private determineErrorCode(error: Error): ErrorCode {
    const message = error.message.toLowerCase();

    if (message.includes('network') || message.includes('fetch')) {
      return ErrorCode.NETWORK_ERROR;
    }
    if (message.includes('timeout')) {
      return ErrorCode.TIMEOUT_ERROR;
    }
    if (message.includes('unauthorized') || message.includes('401')) {
      return ErrorCode.AUTH_UNAUTHORIZED;
    }
    if (message.includes('forbidden') || message.includes('403')) {
      return ErrorCode.AUTH_FORBIDDEN;
    }
    if (message.includes('token') && message.includes('expired')) {
      return ErrorCode.AUTH_TOKEN_EXPIRED;
    }
    if (message.includes('xero')) {
      return ErrorCode.XERO_API_ERROR;
    }
    if (message.includes('validation')) {
      return ErrorCode.VALIDATION_ERROR;
    }

    return ErrorCode.UNKNOWN_ERROR;
  }

  // Show user-friendly notifications
  private showUserNotification(error: AppError): void {
    const userMessage = this.getUserFriendlyMessage(error);

    switch (error.code) {
      case ErrorCode.AUTH_TOKEN_EXPIRED:
      case ErrorCode.AUTH_UNAUTHORIZED:
        toast.error('Session Expired', {
          description: 'Please log in again to continue.',
          duration: 5000,
        });
        break;

      case ErrorCode.NETWORK_ERROR:
      case ErrorCode.CONNECTION_ERROR:
        toast.error('Connection Error', {
          description: 'Please check your internet connection and try again.',
          duration: 5000,
        });
        break;

      case ErrorCode.XERO_CONNECTION_FAILED:
      case ErrorCode.XERO_AUTH_FAILED:
        toast.error('Xero Connection Failed', {
          description: userMessage,
          duration: 5000,
        });
        break;

      default:
        toast.error('Error', {
          description: userMessage,
          duration: 4000,
        });
    }
  }

  // Get user-friendly error messages
  private getUserFriendlyMessage(error: AppError): string {
    switch (error.code) {
      case ErrorCode.NETWORK_ERROR:
        return 'Unable to connect to the server. Please check your internet connection.';
      case ErrorCode.TIMEOUT_ERROR:
        return 'The request took too long to complete. Please try again.';
      case ErrorCode.AUTH_TOKEN_EXPIRED:
        return 'Your session has expired. Please log in again.';
      case ErrorCode.AUTH_INVALID_CREDENTIALS:
        return 'Invalid email or password. Please try again.';
      case ErrorCode.AUTH_UNAUTHORIZED:
        return 'You are not authorized to perform this action.';
      case ErrorCode.XERO_CONNECTION_FAILED:
        return 'Failed to connect to Xero. Please try again later.';
      case ErrorCode.XERO_AUTH_FAILED:
        return 'Xero authorization failed. Please try connecting again.';
      case ErrorCode.API_SERVER_ERROR:
        return 'Server error occurred. Please try again later.';
      case ErrorCode.API_NOT_FOUND:
        return 'The requested resource was not found.';
      case ErrorCode.VALIDATION_ERROR:
        return 'Please check your input and try again.';
      default:
        return error.message || 'An unexpected error occurred. Please try again.';
    }
  }

  // Get error logs for debugging
  getErrorLogs(): AppError[] {
    return [...this.errorLog];
  }

  // Clear error logs
  clearErrorLogs(): void {
    this.errorLog = [];
  }

  // Handle async operations with error handling
  async withErrorHandling<T>(
    operation: () => Promise<T>,
    context?: Record<string, any>
  ): Promise<T | null> {
    try {
      return await operation();
    } catch (error) {
      this.handleError(error, context);
      return null;
    }
  }
}

// Export singleton instance
export const errorHandler = AppErrorHandler.getInstance();

// Utility functions for common error handling patterns
export const handleAsyncError = async <T>(
  operation: () => Promise<T>,
  context?: Record<string, any>
): Promise<T | null> => {
  return errorHandler.withErrorHandling(operation, context);
};

export const createAppError = (
  code: ErrorCode,
  message: string,
  details?: any,
  context?: Record<string, any>
): AppError => {
  return errorHandler.createError(code, message, details, context);
};
