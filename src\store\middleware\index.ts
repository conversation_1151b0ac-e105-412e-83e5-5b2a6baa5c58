// Redux middleware collection
import { Middleware, isRejectedWithValue } from '@reduxjs/toolkit';
import { errorHandler, ErrorCode } from '@/lib/error-handler';
import { addNotification } from '../slices/uiSlice';

// Error handling middleware for async thunks
export const rtkQueryErrorLogger: Middleware = (api) => (next) => (action) => {
  // RTK Query uses `createAsyncThunk` from redux-toolkit under the hood
  if (isRejectedWithValue(action)) {
    const error = action.payload;

    // Handle different types of errors
    if (error?.status === 401) {
      // Handle unauthorized errors
      api.dispatch(addNotification({
        type: 'error',
        title: 'Authentication Error',
        message: 'Your session has expired. Please log in again.',
        duration: 5000,
      }));

      // Redirect to login if needed
      // if (window.location.pathname !== '/login') {
      //   window.location.href = '/login';
      // }
    } else if (error?.status === 403) {
      // Handle forbidden errors
      api.dispatch(addNotification({
        type: 'error',
        title: 'Access Denied',
        message: 'You do not have permission to perform this action.',
        duration: 5000,
      }));
    } else if (error?.status >= 500) {
      // Handle server errors
      api.dispatch(addNotification({
        type: 'error',
        title: 'Server Error',
        message: 'Something went wrong on our end. Please try again later.',
        duration: 5000,
      }));
    }

    // Log error using centralized error handler
    errorHandler.handleError(new Error(error?.message || 'API Error'), {
      action: action.type,
      status: error?.status,
      data: error?.data,
    });
  }

  return next(action);
};



// Action sanitizer for development
export const actionSanitizer = (action: any) => {
  // Remove sensitive data from logged actions
  if (action.type?.includes('auth') && action.payload) {
    return {
      ...action,
      payload: {
        ...action.payload,
        password: action.payload.password ? '[REDACTED]' : undefined,
        token: action.payload.token ? '[REDACTED]' : undefined,
        refreshToken: action.payload.refreshToken ? '[REDACTED]' : undefined,
      },
    };
  }

  return action;
};

// State sanitizer for development
export const stateSanitizer = (state: any) => {
  // Remove sensitive data from logged state
  if (state.auth) {
    return {
      ...state,
      auth: {
        ...state.auth,
        token: state.auth.token ? '[REDACTED]' : null,
        accessToken: state.auth.accessToken ? '[REDACTED]' : null,
        refreshToken: state.auth.refreshToken ? '[REDACTED]' : null,
      },
    };
  }

  return state;
};

// Persistence middleware for saving important state to localStorage
export const persistenceMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);

  // Save certain state changes to localStorage
  const state = store.getState() as any;

  // Persist UI preferences
  if (action.type?.startsWith('ui/')) {
    try {
      localStorage.setItem('ui_preferences', JSON.stringify({
        theme: state.ui.theme,
        layout: state.ui.layout,
        preferences: state.ui.preferences,
        features: state.ui.features,
      }));
    } catch (error) {
      console.warn('Failed to persist UI preferences:', error);
    }
  }

  // Persist auth state (without sensitive tokens)
  if (action.type?.startsWith('auth/')) {
    try {
      localStorage.setItem('auth_state', JSON.stringify({
        isAuthenticated: state.auth.isAuthenticated,
        isConnected: state.auth.isConnected,
        user: state.auth.user,
        companyInfo: state.auth.companyInfo,
      }));
    } catch (error) {
      console.warn('Failed to persist auth state:', error);
    }
  }

  return result;
};

// Analytics middleware for tracking user actions
export const analyticsMiddleware: Middleware = (store) => (next) => (action) => {
  const result = next(action);

  // Track important user actions
  const trackableActions = [
    'auth/loginUser/fulfilled',
    'auth/logoutUser/fulfilled',
    'auth/completeXeroOAuth/fulfilled',
    'companies/setSelectedOrganization',
    'settings/syncEntity/fulfilled',
  ];

  if (trackableActions.includes(action.type)) {
    // Here you would integrate with your analytics service
    // For now, just log in development
 
  }

  return result;
};

// Export all middleware
export const customMiddleware = [
  rtkQueryErrorLogger,
  persistenceMiddleware,
  analyticsMiddleware,
];
