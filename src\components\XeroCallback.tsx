// module-scope variable to guard against double calls
let hasXeroCallbackRun = false;

import React, { useEffect, useState, useCallback } from "react";
import { useNavigate, useSearchParams } from "react-router-dom";
import { useAppDispatch } from "@/store/hooks";
import { completeXeroOAuth } from "@/store/actions/auth.actions";
import { fetchOrganizations } from "@/store/actions/companies.actions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { CheckCircle, AlertCircle, Loader2, Building } from "lucide-react";
import { toast } from "@/components/ui/sonner";
import "@/assets/css/XeroCallback.css";

interface CallbackState {
  status: "loading" | "success" | "error";
  message: string;
  companyInfo?: {
    name: string;
    shortCode?: string;
    baseCurrency?: string;
  };
}

const XeroCallback: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const dispatch = useAppDispatch();

  const [callbackState, setCallbackState] = useState<CallbackState>({
    status: "loading",
    message: "Connecting to Xero...",
  });

  const handleCallback = useCallback(async () => {
    if (hasXeroCallbackRun) {
      console.info("Xero callback already processed, skipping...");
      return;
    }
    hasXeroCallbackRun = true;

    try {
      const code = searchParams.get("code");
      const state = searchParams.get("state");
      const error = searchParams.get("error");

      if (error) {
        const errorDescription =
          searchParams.get("error_description") || "Authorization failed";
        setCallbackState({
          status: "error",
          message: `OAuth Error: ${errorDescription}`,
        });

        toast.error("Connection Failed", {
          description: errorDescription,
          duration: 5000,
        });
        return;
      }

      if (!code) {
        setCallbackState({
          status: "error",
          message: "Missing authorization code from Xero",
        });

        toast.error("Connection Failed", {
          description: "Invalid callback parameters",
          duration: 5000,
        });
        return;
      }

      setCallbackState({
        status: "loading",
        message: "Exchanging authorization code...",
      });

      const result = await dispatch(
        completeXeroOAuth({
          code,
          state: state || undefined,
        }) as any
      ).unwrap();

      if (result.success) {
        setCallbackState({
          status: "success",
          message: "Successfully connected to Xero!",
          companyInfo: {
            name: result.data.Name,
            shortCode: result.data.ShortCode || "",
            baseCurrency: result.data.BaseCurrency || "",
          },
        });

        toast.success("Connection Successful", {
          description: `Connected to ${result.data.Name || "Xero"}`,
          duration: 4000,
        });

        // Refresh companies list after successful OAuth connection
        try {
          setCallbackState((prev) => ({
            ...prev,
            message: "Updating company list...",
          }));

          await dispatch(fetchOrganizations() as any).unwrap();

          setCallbackState((prev) => ({
            ...prev,
            message: "Successfully connected to Xero!",
          }));

          toast.success("Company List Updated", {
            description: "Your connected companies have been refreshed",
            duration: 3000,
          });
        } catch (fetchError) {
          console.warn("Failed to refresh companies after OAuth:", fetchError);
          // Don't fail the entire process if companies fetch fails
          toast.info(
            "Connection successful, but company list may need manual refresh",
            {
              duration: 4000,
            }
          );
        }

        // if you still want to redirect later, keep this
        // setTimeout(() => navigate("/dashboard"), 2000);
      } else {
        throw new Error("OAuth completion failed");
      }
    } catch (error) {
      console.error("Xero callback error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Unknown error occurred";
      setCallbackState({
        status: "error",
        message: errorMessage,
      });
      toast.error("Connection Failed", {
        description: errorMessage,
        duration: 5000,
      });
    }
  }, [searchParams, dispatch, navigate]);

  useEffect(() => {
    const code = searchParams.get("code");
    const error = searchParams.get("error");

    if (code || error) {
      handleCallback();
    } else {
      console.info("No OAuth parameters found, skipping callback handling");
    }
  }, [handleCallback, searchParams]);

  const handleGoToDashboard = () => {
    navigate("/dashboard");
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center p-4">
      <div className="w-full max-w-md">
        <Card className="card-entrance shadow-xl border-0">
          <CardHeader className="text-center pb-4">
            <div className="w-20 h-20 rounded-full flex items-center justify-center mx-auto mb-4 xero-primary">
              <span className="text-white font-bold text-2xl">xero</span>
            </div>
            <CardTitle className="text-2xl font-bold text-gray-800">
              Xero Integration
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* Loading State */}
            {callbackState.status === "loading" && (
              <div className="text-center animate-fade-in">
                <div className="flex justify-center mb-4">
                  <Loader2 className="h-12 w-12 text-blue-500 spin-slow" />
                </div>
                <p className="text-gray-600">{callbackState.message}</p>
                <p className="text-sm text-gray-500 mt-2">
                  Please wait while we complete the connection...
                </p>
              </div>
            )}

            {/* Success State */}
            {callbackState.status === "success" &&
              callbackState.companyInfo && (
                <div className="text-center animate-fade-in">
                  <div className="flex justify-center mb-4">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
                      <CheckCircle className="h-8 w-8 text-green-600" />
                    </div>
                  </div>
                  <h3 className="text-xl font-semibold text-green-800 mb-2">
                    Connection Successful!
                  </h3>
                  <p className="text-gray-600 mb-4">{callbackState.message}</p>

                  {/* Company Info */}
                  <div className="bg-gray-50 rounded-lg p-4 mb-4">
                    <div className="flex items-center justify-center mb-2">
                      <Building className="h-5 w-5 text-gray-600 mr-2" />
                      <span className="font-medium text-gray-800">
                        {callbackState.companyInfo.name}
                      </span>
                    </div>
                    {callbackState.companyInfo.shortCode && (
                      <p className="text-sm text-gray-600">
                        Code: {callbackState.companyInfo.shortCode}
                      </p>
                    )}
                    {callbackState.companyInfo.baseCurrency && (
                      <p className="text-sm text-gray-600">
                        Currency: {callbackState.companyInfo.baseCurrency}
                      </p>
                    )}
                  </div>

                  <Button
                    onClick={handleGoToDashboard}
                    className="mt-4 w-full bg-green-600 hover:bg-green-700"
                  >
                    Go to Dashboard
                  </Button>
                </div>
              )}

            {/* Error State */}
            {callbackState.status === "error" && (
              <div className="text-center animate-fade-in">
                <div className="flex justify-center mb-4">
                  <div className="w-16 h-16 bg-red-100 rounded-full flex items-center justify-center">
                    <AlertCircle className="h-8 w-8 text-red-600" />
                  </div>
                </div>
                <h3 className="text-xl font-semibold text-red-800 mb-2">
                  Connection Failed
                </h3>
                <p className="text-gray-600 mb-4">{callbackState.message}</p>
                <p className="text-sm text-gray-500 mb-6">
                  Please try connecting again or contact support if the issue
                  persists.
                </p>
                <Button onClick={handleGoToDashboard} className="w-full">
                  Return to Dashboard
                </Button>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default XeroCallback;
