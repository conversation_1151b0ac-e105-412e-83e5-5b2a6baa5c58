import React from 'react';
import { cn } from '@/lib/utils';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { 
  FileX, 
  Search, 
  Database, 
  Wifi, 
  AlertCircle, 
  Plus,
  RefreshCw,
  LucideIcon 
} from 'lucide-react';

export interface EmptyStateProps {
  icon?: LucideIcon;
  title: string;
  description?: string;
  action?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'secondary';
    icon?: LucideIcon;
  };
  secondaryAction?: {
    label: string;
    onClick: () => void;
    variant?: 'default' | 'outline' | 'secondary';
    icon?: LucideIcon;
  };
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'error' | 'warning' | 'info';
}

const sizeClasses = {
  sm: {
    container: 'py-8',
    icon: 'h-8 w-8',
    title: 'text-lg',
    description: 'text-sm',
  },
  md: {
    container: 'py-12',
    icon: 'h-12 w-12',
    title: 'text-xl',
    description: 'text-base',
  },
  lg: {
    container: 'py-16',
    icon: 'h-16 w-16',
    title: 'text-2xl',
    description: 'text-lg',
  },
};

const variantClasses = {
  default: {
    icon: 'text-gray-400',
    title: 'text-gray-900',
    description: 'text-gray-600',
  },
  error: {
    icon: 'text-red-400',
    title: 'text-red-900',
    description: 'text-red-600',
  },
  warning: {
    icon: 'text-yellow-400',
    title: 'text-yellow-900',
    description: 'text-yellow-600',
  },
  info: {
    icon: 'text-blue-400',
    title: 'text-blue-900',
    description: 'text-blue-600',
  },
};

export const EmptyState: React.FC<EmptyStateProps> = ({
  icon: Icon = FileX,
  title,
  description,
  action,
  secondaryAction,
  className,
  size = 'md',
  variant = 'default',
}) => {
  const sizeClass = sizeClasses[size];
  const variantClass = variantClasses[variant];

  return (
    <div className={cn(
      'flex flex-col items-center justify-center text-center',
      sizeClass.container,
      className
    )}>
      <div className={cn(
        'rounded-full bg-gray-100 p-4 mb-4',
        variant === 'error' && 'bg-red-100',
        variant === 'warning' && 'bg-yellow-100',
        variant === 'info' && 'bg-blue-100'
      )}>
        <Icon className={cn(sizeClass.icon, variantClass.icon)} />
      </div>
      
      <h3 className={cn(
        'font-semibold mb-2',
        sizeClass.title,
        variantClass.title
      )}>
        {title}
      </h3>
      
      {description && (
        <p className={cn(
          'mb-6 max-w-md',
          sizeClass.description,
          variantClass.description
        )}>
          {description}
        </p>
      )}
      
      {(action || secondaryAction) && (
        <div className="flex flex-col sm:flex-row gap-3">
          {action && (
            <Button
              onClick={action.onClick}
              variant={action.variant || 'default'}
              className="flex items-center gap-2"
            >
              {action.icon && <action.icon className="h-4 w-4" />}
              {action.label}
            </Button>
          )}
          
          {secondaryAction && (
            <Button
              onClick={secondaryAction.onClick}
              variant={secondaryAction.variant || 'outline'}
              className="flex items-center gap-2"
            >
              {secondaryAction.icon && <secondaryAction.icon className="h-4 w-4" />}
              {secondaryAction.label}
            </Button>
          )}
        </div>
      )}
    </div>
  );
};

// Specialized empty state components
export const NoDataFound: React.FC<{
  title?: string;
  description?: string;
  onRefresh?: () => void;
  onCreate?: () => void;
}> = ({
  title = 'No data found',
  description = 'There are no items to display at the moment.',
  onRefresh,
  onCreate,
}) => (
  <EmptyState
    icon={Database}
    title={title}
    description={description}
    action={onCreate ? {
      label: 'Create New',
      onClick: onCreate,
      icon: Plus,
    } : undefined}
    secondaryAction={onRefresh ? {
      label: 'Refresh',
      onClick: onRefresh,
      variant: 'outline',
      icon: RefreshCw,
    } : undefined}
  />
);

export const SearchNotFound: React.FC<{
  query?: string;
  onClearSearch?: () => void;
}> = ({
  query,
  onClearSearch,
}) => (
  <EmptyState
    icon={Search}
    title="No results found"
    description={query ? `No results found for "${query}". Try adjusting your search terms.` : 'No results found. Try adjusting your search terms.'}
    action={onClearSearch ? {
      label: 'Clear Search',
      onClick: onClearSearch,
      variant: 'outline',
    } : undefined}
  />
);

export const ConnectionError: React.FC<{
  onRetry?: () => void;
}> = ({ onRetry }) => (
  <EmptyState
    icon={Wifi}
    title="Connection Error"
    description="Unable to connect to the server. Please check your internet connection and try again."
    variant="error"
    action={onRetry ? {
      label: 'Try Again',
      onClick: onRetry,
      icon: RefreshCw,
    } : undefined}
  />
);

export const ErrorState: React.FC<{
  title?: string;
  description?: string;
  onRetry?: () => void;
}> = ({
  title = 'Something went wrong',
  description = 'An error occurred while loading the data. Please try again.',
  onRetry,
}) => (
  <EmptyState
    icon={AlertCircle}
    title={title}
    description={description}
    variant="error"
    action={onRetry ? {
      label: 'Try Again',
      onClick: onRetry,
      icon: RefreshCw,
    } : undefined}
  />
);

// Card wrapper for empty states
export const EmptyStateCard: React.FC<EmptyStateProps & {
  cardClassName?: string;
}> = ({ cardClassName, ...props }) => (
  <Card className={cn('border-dashed', cardClassName)}>
    <CardContent className="p-0">
      <EmptyState {...props} />
    </CardContent>
  </Card>
);

export default EmptyState;
