/**
 * Authentication utility functions for token management and validation
 */

interface TokenPayload {
  exp: number;
  iat: number;
  userId: string;
  email: string;
}

/**
 * Decode JWT token without verification (for client-side expiry checking)
 * Note: This is only for reading the payload, not for security validation
 */
export const decodeJWT = (token: string): TokenPayload | null => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) {
      return null;
    }

    const payload = parts[1];
    const decoded = atob(payload.replace(/-/g, '+').replace(/_/g, '/'));
    return JSON.parse(decoded);
  } catch (error) {
    console.error('Failed to decode JWT:', error);
    return null;
  }
};

/**
 * Check if a JWT token is expired
 * @param token - JWT token string
 * @param bufferMinutes - Buffer time in minutes before actual expiry (default: 5 minutes)
 * @returns true if token is expired or will expire within buffer time
 */
export const isTokenExpired = (token: string | null, bufferMinutes: number = 5): boolean => {
  if (!token) {
    return true;
  }

  const payload = decodeJWT(token);
  if (!payload || !payload.exp) {
    return true;
  }

  const currentTime = Math.floor(Date.now() / 1000);
  const bufferTime = bufferMinutes * 60;
  
  // Check if token expires within the buffer time
  return payload.exp <= (currentTime + bufferTime);
};

/**
 * Get token expiry time as a Date object
 */
export const getTokenExpiryDate = (token: string | null): Date | null => {
  if (!token) {
    return null;
  }

  const payload = decodeJWT(token);
  if (!payload || !payload.exp) {
    return null;
  }

  return new Date(payload.exp * 1000);
};

/**
 * Get time remaining until token expires
 * @param token - JWT token string
 * @returns Object with days, hours, minutes, seconds remaining, or null if expired/invalid
 */
export const getTokenTimeRemaining = (token: string | null): {
  days: number;
  hours: number;
  minutes: number;
  seconds: number;
  totalSeconds: number;
} | null => {
  if (!token) {
    return null;
  }

  const payload = decodeJWT(token);
  if (!payload || !payload.exp) {
    return null;
  }

  const currentTime = Math.floor(Date.now() / 1000);
  const timeRemaining = payload.exp - currentTime;

  if (timeRemaining <= 0) {
    return null;
  }

  const days = Math.floor(timeRemaining / (24 * 60 * 60));
  const hours = Math.floor((timeRemaining % (24 * 60 * 60)) / (60 * 60));
  const minutes = Math.floor((timeRemaining % (60 * 60)) / 60);
  const seconds = timeRemaining % 60;

  return {
    days,
    hours,
    minutes,
    seconds,
    totalSeconds: timeRemaining
  };
};

/**
 * Check if user needs to re-authenticate
 * This checks both token existence and expiry
 */
export const needsAuthentication = (): boolean => {
  const token = localStorage.getItem('auth_token');
  return isTokenExpired(token);
};

/**
 * Clear all authentication tokens from localStorage
 */
export const clearAuthTokens = (): void => {
  localStorage.removeItem('auth_token');
  localStorage.removeItem('xero_access_token');
  localStorage.removeItem('xero_refresh_token');
};

/**
 * Get user info from token payload
 */
export const getUserFromToken = (token: string | null): { userId: string; email: string } | null => {
  if (!token) {
    return null;
  }

  const payload = decodeJWT(token);
  if (!payload || !payload.userId || !payload.email) {
    return null;
  }

  return {
    userId: payload.userId,
    email: payload.email
  };
};

/**
 * Format time remaining in a human-readable format
 */
export const formatTimeRemaining = (token: string | null): string => {
  const timeRemaining = getTokenTimeRemaining(token);
  
  if (!timeRemaining) {
    return 'Expired';
  }

  const { days, hours, minutes } = timeRemaining;

  if (days > 0) {
    return `${days} day${days > 1 ? 's' : ''} remaining`;
  } else if (hours > 0) {
    return `${hours} hour${hours > 1 ? 's' : ''} remaining`;
  } else if (minutes > 0) {
    return `${minutes} minute${minutes > 1 ? 's' : ''} remaining`;
  } else {
    return 'Expires soon';
  }
};
