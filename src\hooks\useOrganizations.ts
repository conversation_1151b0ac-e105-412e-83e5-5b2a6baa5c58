// Custom hook for managing organizations data and preventing duplicate API calls
// Provides centralized organization loading logic with proper caching

import { useEffect, useCallback, useMemo } from 'react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchOrganizations } from '@/store/actions/companies.actions';
import { setSelectedOrganization } from '@/store/slices/companiesSlice';
import { toast } from '@/components/ui/sonner';

/**
 * Configuration options for the useOrganizations hook
 */
interface UseOrganizationsOptions {
  /** Whether to automatically load organizations on mount */
  autoLoad?: boolean;
  /** Whether to auto-select the first connected organization */
  autoSelect?: boolean;
  /** Whether to show error toasts */
  showErrorToasts?: boolean;
  /** Whether to show success toasts on refresh */
  showSuccessToasts?: boolean;
  /** Whether to show loading overlay during API calls */
  showLoadingOverlay?: boolean;
}

/**
 * Return type for the useOrganizations hook
 */
interface UseOrganizationsReturn {
  /** All organizations from Redux store */
  organizations: any[];
  /** Currently selected organization ID */
  selectedOrganization: string | null;
  /** Loading state */
  isLoading: boolean;
  /** Error state */
  error: string | null;
  /** Connected organizations only */
  connectedOrganizations: any[];
  /** Whether there are any connected organizations */
  hasConnectedOrganizations: boolean;
  /** Function to manually refresh organizations */
  refreshOrganizations: () => Promise<void>;
  /** Function to select an organization */
  selectOrganization: (organizationId: string) => void;
  /** Whether organizations have been loaded at least once */
  isLoaded: boolean;
}

/**
 * Custom hook for managing organizations data
 * 
 * Features:
 * - Prevents duplicate API calls across components
 * - Provides centralized loading logic
 * - Auto-selection of first connected organization
 * - Proper error handling and user feedback
 * - Memoized computed values for performance
 * 
 * @param options Configuration options
 * @returns Organizations data and management functions
 */
export const useOrganizations = (options: UseOrganizationsOptions = {}): UseOrganizationsReturn => {
  const {
    autoLoad = true,
    autoSelect = true,
    showErrorToasts = true,
    showSuccessToasts = true,
    showLoadingOverlay = false,
  } = options;

  const dispatch = useAppDispatch();

  // Redux state selectors
  const {
    organizations,
    selectedOrganization,
    isLoading,
    error,
  } = useAppSelector((state) => state.companies);

  // Memoized computed values
  const connectedOrganizations = useMemo(() => {
    return organizations.filter((org) => {
      const status = org.organizationStatus || (org as any).ConnectionStatus;
      return status === "ACTIVE" || status === "CONNECTED";
    });
  }, [organizations]);

  const hasConnectedOrganizations = useMemo(() => {
    return connectedOrganizations.length > 0;
  }, [connectedOrganizations.length]);

  const isLoaded = useMemo(() => {
    return organizations.length > 0;
  }, [organizations.length]);

  /**
   * Load organizations with proper error handling
   */
  const loadOrganizations = useCallback(async () => {
    try {
      await dispatch(fetchOrganizations()).unwrap();
    } catch (error) {
      console.error("Failed to load organizations:", error);
      if (showErrorToasts) {
        toast.error("Failed to load organizations", {
          description: "Please refresh the page to try again.",
          duration: 4000,
        });
      }
    }
  }, [dispatch, showErrorToasts]);

  /**
   * Refresh organizations with user feedback
   */
  const refreshOrganizations = useCallback(async () => {
    try {
      await dispatch(fetchOrganizations()).unwrap();
      if (showSuccessToasts) {
        toast.success("Organizations refreshed", {
          description: "Latest organization data has been loaded.",
          duration: 2000,
        });
      }
    } catch (error) {
      console.error("Failed to refresh organizations:", error);
      if (showErrorToasts) {
        toast.error("Failed to refresh organizations", {
          description: "Please try again later.",
          duration: 3000,
        });
      }
      throw error; // Re-throw for component-specific handling
    }
  }, [dispatch, showErrorToasts, showSuccessToasts]);

  /**
   * Select an organization with user feedback
   */
  const selectOrganization = useCallback((organizationId: string) => {
    dispatch(setSelectedOrganization(organizationId));
    const organization = organizations.find(org => org.id === organizationId);
    if (showSuccessToasts && organization) {
      toast.success("Organization selected", {
        description: `Switched to ${organization.name}`,
        duration: 2000,
      });
    }
  }, [dispatch, organizations, showSuccessToasts]);

  /**
   * Auto-load organizations on mount if not already loaded
   */
  useEffect(() => {
    if (autoLoad && !isLoaded && !isLoading) {
      loadOrganizations();
    }
  }, [autoLoad, isLoaded, isLoading, loadOrganizations]);

  /**
   * Auto-select first connected organization when organizations are loaded
   */
  useEffect(() => {
    if (autoSelect && !selectedOrganization && hasConnectedOrganizations) {
      dispatch(setSelectedOrganization(connectedOrganizations[0].id));
    }
  }, [autoSelect, selectedOrganization, hasConnectedOrganizations, connectedOrganizations, dispatch]);

  return {
    organizations,
    selectedOrganization,
    isLoading,
    error,
    connectedOrganizations,
    hasConnectedOrganizations,
    refreshOrganizations,
    selectOrganization,
    isLoaded,
  };
};

/**
 * Hook for components that only need to read organizations data
 * without triggering any side effects or API calls
 */
export const useOrganizationsReadOnly = () => {
  return useOrganizations({
    autoLoad: false,
    autoSelect: false,
    showErrorToasts: false,
    showSuccessToasts: false,
  });
};

export default useOrganizations;
