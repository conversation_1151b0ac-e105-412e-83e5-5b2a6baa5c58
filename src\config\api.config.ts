// API Configuration for different environments
// This file centralizes API configuration to match backend CORS settings

export interface ApiConfig {
  baseURL: string;
  timeout: number;
  withCredentials: boolean;
  retryAttempts: number;
  retryDelay: number;
  corsOrigins: string[];
  headers: {
    'Content-Type': string;
    'Accept': string;
    'Accept-Language': string;
    'Accept-Encoding': string;
    'Cache-Control': string;
    'Pragma': string;
  };
}

// Environment-specific configurations
const configs: Record<string, ApiConfig> = {
  development: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:9000/api/v1',
    timeout: 30000,
    withCredentials: true,
    retryAttempts: 3,
    retryDelay: 1000,
    corsOrigins: ['http://localhost:3000', 'http://localhost:8080', 'http://localhost:5173'],
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
    },
  },

  staging: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api-staging.yourdomain.com/api/v1',
    timeout: 30000,
    withCredentials: true,
    retryAttempts: 2,
    retryDelay: 1500,
    corsOrigins: ['https://staging.yourdomain.com'],
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
    },
  },

  production: {
    baseURL: import.meta.env.VITE_API_BASE_URL || 'https://api.yourdomain.com/api/v1',
    timeout: 25000,
    withCredentials: true,
    retryAttempts: 2,
    retryDelay: 2000,
    corsOrigins: ['https://yourdomain.com', 'https://app.yourdomain.com'],
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Accept-Language': 'en-US,en;q=0.9',
      'Accept-Encoding': 'gzip, deflate, br',
      'Cache-Control': 'no-cache',
      'Pragma': 'no-cache',
    },
  },
};

// Get current environment
const getCurrentEnvironment = (): string => {
  if (import.meta.env.PROD) return 'production';
  if (import.meta.env.VITE_APP_ENV === 'staging') return 'staging';
  return 'development';
};

// Get configuration for current environment
export const getApiConfig = (): ApiConfig => {
  const env = getCurrentEnvironment();
  return configs[env] || configs.development;
};

// Headers that should be sent with every request (matching CORS allowedHeaders)
export const getDefaultHeaders = () => {
  const config = getApiConfig();
  return {
    ...config.headers,
    'X-Client-Version': '1.0.0',
    'X-Client-Platform': 'web',
    'Origin': typeof window !== 'undefined' ? window.location.origin : undefined,
  };
};

// Generate correlation ID for request tracking
export const generateCorrelationId = (): string => {
  return `corr_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Generate request ID for tracking
export const generateRequestId = (): string => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Check if current origin is allowed
export const isOriginAllowed = (): boolean => {
  if (typeof window === 'undefined') return true;

  const config = getApiConfig();
  const currentOrigin = window.location.origin;

  return config.corsOrigins.includes(currentOrigin);
};

// Validate CORS configuration
export const validateCorsConfig = () => {
  const config = getApiConfig();
  const currentOrigin = typeof window !== 'undefined' ? window.location.origin : 'unknown';

  return {
    environment: getCurrentEnvironment(),
    currentOrigin,
    allowedOrigins: config.corsOrigins,
    isOriginAllowed: isOriginAllowed(),
    withCredentials: config.withCredentials,
    baseURL: config.baseURL,
  };
};

export default getApiConfig;
