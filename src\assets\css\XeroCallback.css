/* XeroCallback Component Styles */

/* Fade-in Animation */
@keyframes fade-in {
    from {
        opacity: 0;
        transform: translateY(10px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.animate-fade-in {
    animation: fade-in 0.6s ease-out;
}

/* Enhanced <PERSON>unce Animation for Loading Dots */
@keyframes enhanced-bounce {

    0%,
    80%,
    100% {
        transform: scale(0.8);
        opacity: 0.7;
    }

    40% {
        transform: scale(1.2);
        opacity: 1;
    }
}

.loading-dot {
    animation: enhanced-bounce 1.4s infinite;
}

.loading-dot:nth-child(1) {
    animation-delay: 0s;
}

.loading-dot:nth-child(2) {
    animation-delay: 0.2s;
}

.loading-dot:nth-child(3) {
    animation-delay: 0.4s;
}

/* Progress Bar Animation */
@keyframes progress-flow {
    0% {
        transform: translateX(-100%);
    }

    100% {
        transform: translateX(100%);
    }
}

.progress-bar-animated::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: 30%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    animation: progress-flow 2s infinite;
}

/* Success Check Animation */
@keyframes success-check {
    0% {
        transform: scale(0);
        opacity: 0;
    }

    50% {
        transform: scale(1.2);
        opacity: 1;
    }

    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.success-check {
    animation: success-check 0.8s ease-out;
}

/* Success Circle Pulse */
@keyframes success-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0.4);
    }

    70% {
        box-shadow: 0 0 0 20px rgba(34, 197, 94, 0);
    }

    100% {
        box-shadow: 0 0 0 0 rgba(34, 197, 94, 0);
    }
}

.success-circle {
    animation: success-pulse 2s infinite;
}

/* Card Entrance Animation */
@keyframes card-entrance {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }

    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.card-entrance {
    animation: card-entrance 0.5s ease-out;
}

/* Status Icon Animations */
@keyframes spin-slow {
    from {
        transform: rotate(0deg);
    }

    to {
        transform: rotate(360deg);
    }
}

.spin-slow {
    animation: spin-slow 2s linear infinite;
}

/* Error Shake Animation */
@keyframes error-shake {

    0%,
    100% {
        transform: translateX(0);
    }

    10%,
    30%,
    50%,
    70%,
    90% {
        transform: translateX(-2px);
    }

    20%,
    40%,
    60%,
    80% {
        transform: translateX(2px);
    }
}

.error-shake {
    animation: error-shake 0.6s ease-in-out;
}

/* Button Hover Effects */
.btn-success-hover {
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-success-hover::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-success-hover:hover::before {
    left: 100%;
}

/* Loading Text Animation */
@keyframes loading-text {

    0%,
    100% {
        opacity: 1;
    }

    50% {
        opacity: 0.5;
    }
}

.loading-text {
    animation: loading-text 2s ease-in-out infinite;
}

/* Xero Branding Colors */
.xero-primary {
    background-color: #13b5ea;
}

.xero-secondary {
    background-color: #24d0ff;
}

.xero-accent {
    background-color: #ffa500;
}

/* Responsive Design */
@media (max-width: 640px) {
    .animate-fade-in {
        animation-duration: 0.4s;
    }

    .card-entrance {
        animation-duration: 0.3s;
    }
}