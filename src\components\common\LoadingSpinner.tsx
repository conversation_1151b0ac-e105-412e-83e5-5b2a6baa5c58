import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

export interface LoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg' | 'xl';
  variant?: 'default' | 'primary' | 'secondary' | 'muted';
  className?: string;
  text?: string;
  fullScreen?: boolean;
  overlay?: boolean;
}

const sizeClasses = {
  sm: 'h-4 w-4',
  md: 'h-6 w-6',
  lg: 'h-8 w-8',
  xl: 'h-12 w-12',
};

const variantClasses = {
  default: 'text-gray-600',
  primary: 'text-blue-600',
  secondary: 'text-gray-400',
  muted: 'text-gray-300',
};

export const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  size = 'md',
  variant = 'default',
  className,
  text,
  fullScreen = false,
  overlay = false,
}) => {
  const spinnerContent = (
    <div className={cn(
      'flex flex-col items-center justify-center gap-2',
      fullScreen && 'min-h-screen',
      className
    )}>
      <Loader2 
        className={cn(
          'animate-spin',
          sizeClasses[size],
          variantClasses[variant]
        )} 
      />
      {text && (
        <p className={cn(
          'text-sm font-medium',
          variantClasses[variant]
        )}>
          {text}
        </p>
      )}
    </div>
  );

  if (overlay) {
    return (
      <div className="fixed inset-0 bg-black/20 backdrop-blur-sm z-50 flex items-center justify-center">
        <div className="bg-white rounded-lg p-6 shadow-xl">
          {spinnerContent}
        </div>
      </div>
    );
  }

  return spinnerContent;
};

// Specialized loading components
export const PageLoader: React.FC<{ text?: string }> = ({ text = 'Loading...' }) => (
  <LoadingSpinner 
    size="lg" 
    variant="primary" 
    text={text} 
    fullScreen 
    className="bg-gray-50/80" 
  />
);

export const ButtonLoader: React.FC<{ size?: 'sm' | 'md' }> = ({ size = 'sm' }) => (
  <LoadingSpinner size={size} variant="secondary" className="mr-2" />
);

export const OverlayLoader: React.FC<{ text?: string }> = ({ text = 'Processing...' }) => (
  <LoadingSpinner 
    size="lg" 
    variant="primary" 
    text={text} 
    overlay 
  />
);

export const InlineLoader: React.FC<{ text?: string; size?: 'sm' | 'md' }> = ({ 
  text = 'Loading...', 
  size = 'sm' 
}) => (
  <div className="flex items-center gap-2 py-2">
    <LoadingSpinner size={size} variant="muted" />
    <span className="text-sm text-gray-500">{text}</span>
  </div>
);

export default LoadingSpinner;
