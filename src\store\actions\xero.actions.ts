// Xero Integration Redux Actions
// Handles all Xero OAuth2 and integration-related async thunks

import { createAsyncThunk } from '@reduxjs/toolkit';
import { xeroApi, XeroAuthUrlResponse, XeroCallbackResponse, XeroConnectionStatus, XeroTokenRefreshResponse } from '../../api/xero.api';
import type { RootState, AppDispatch } from '../index';

/**
 * Generate Xero OAuth2 authorization URL
 * GET /api/v1/xero/connect
 */
export const getXeroAuthUrl = createAsyncThunk<
  XeroAuthUrlResponse,
  void,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'xero/getAuthUrl',
  async (_, { rejectWithValue }) => {
    try {
      const response = await xeroApi.getAuthUrl();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get Xero auth URL');
    }
  }
);

/**
 * Handle Xero OAuth2 callback
 * GET /api/v1/xero/callback
 */
export const handleXeroCallback = createAsyncThunk<
  XeroCallbackResponse,
  { code: string; state: string; scope?: string },
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'xero/handleCallback',
  async (params, { rejectWithValue }) => {
    try {
      const response = await xeroApi.handleCallback(params);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to handle Xero callback');
    }
  }
);

/**
 * Disconnect Xero connection
 * POST /api/v1/xero/disconnect
 */
export const disconnectXero = createAsyncThunk<
  { success: boolean; message: string },
  void,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'xero/disconnect',
  async (_, { rejectWithValue }) => {
    try {
      const response = await xeroApi.disconnect();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to disconnect Xero');
    }
  }
);

/**
 * Manually refresh Xero tokens
 * POST /api/v1/xero/refresh
 */
export const refreshXeroTokens = createAsyncThunk<
  XeroTokenRefreshResponse,
  void,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'xero/refreshTokens',
  async (_, { rejectWithValue }) => {
    try {
      const response = await xeroApi.refreshTokens();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to refresh Xero tokens');
    }
  }
);

/**
 * Get current Xero connection status
 * Helper action to check connection status
 */
export const getXeroConnectionStatus = createAsyncThunk<
  XeroConnectionStatus,
  void,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'xero/getConnectionStatus',
  async (_, { rejectWithValue }) => {
    try {
      const response = await xeroApi.getConnectionStatus();
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get Xero connection status');
    }
  }
);

// Export all Xero actions
export const xeroActions = {
  getXeroAuthUrl,
  handleXeroCallback,
  disconnectXero,
  refreshXeroTokens,
  getXeroConnectionStatus,
};

export default xeroActions;
