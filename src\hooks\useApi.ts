// Enhanced API hooks for React components
import { useState, useEffect, useCallback, useRef } from 'react';
import { AxiosRequestConfig, AxiosError } from 'axios';
import { apiRequest, isNetworkError, isTimeoutError } from '@/api/axios.config';
import { errorH<PERSON><PERSON>, ErrorCode } from '@/lib/error-handler';
import { useAppDispatch } from '@/store/hooks';
import { setLoading } from '@/store/slices/uiSlice';

// Types
export interface ApiState<T> {
  data: T | null;
  loading: boolean;
  error: AxiosError | null;
  success: boolean;
}

export interface UseApiOptions {
  immediate?: boolean;
  loadingKey?: string;
  onSuccess?: (data: any) => void;
  onError?: (error: AxiosError) => void;
  retries?: number;
  retryDelay?: number;
}

export interface UseApiReturn<T> extends ApiState<T> {
  execute: (config?: AxiosRequestConfig) => Promise<T | null>;
  reset: () => void;
  cancel: () => void;
}

// Main useApi hook
export function useApi<T = any>(
  config: AxiosRequestConfig,
  options: UseApiOptions = {}
): UseApiReturn<T> {
  const {
    immediate = false,
    loadingKey,
    onSuccess,
    onError,
    retries = 0,
    retryDelay = 1000,
  } = options;

  const dispatch = useAppDispatch();
  const abortControllerRef = useRef<AbortController | null>(null);
  const mountedRef = useRef(true);

  const [state, setState] = useState<ApiState<T>>({
    data: null,
    loading: false,
    error: null,
    success: false,
  });

  // Cancel any ongoing request
  const cancel = useCallback(() => {
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
      abortControllerRef.current = null;
    }
  }, []);

  // Reset state
  const reset = useCallback(() => {
    setState({
      data: null,
      loading: false,
      error: null,
      success: false,
    });
  }, []);

  // Execute API request
  const execute = useCallback(async (overrideConfig?: AxiosRequestConfig): Promise<T | null> => {
    // Cancel any existing request
    cancel();

    // Create new abort controller
    abortControllerRef.current = new AbortController();
    
    const finalConfig = {
      ...config,
      ...overrideConfig,
      signal: abortControllerRef.current.signal,
    };

    // Set loading state
    setState(prev => ({ ...prev, loading: true, error: null, success: false }));
    
    if (loadingKey) {
      dispatch(setLoading({ key: loadingKey, loading: true }));
    }

    let attempt = 0;
    const maxAttempts = retries + 1;

    while (attempt < maxAttempts) {
      try {
        const { data, error } = await apiRequest<T>(finalConfig);

        if (!mountedRef.current) return null;

        if (error) {
          // Check if we should retry
          if (attempt < maxAttempts - 1 && (isNetworkError(error) || isTimeoutError(error))) {
            attempt++;
            await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
            continue;
          }

          // Handle error
          setState(prev => ({ ...prev, loading: false, error, success: false }));
          
          if (loadingKey) {
            dispatch(setLoading({ key: loadingKey, loading: false }));
          }

          onError?.(error);
          errorHandler.handleError(error, { context: 'useApi', config: finalConfig });
          
          return null;
        }

        // Success
        setState(prev => ({ ...prev, data, loading: false, error: null, success: true }));
        
        if (loadingKey) {
          dispatch(setLoading({ key: loadingKey, loading: false }));
        }

        onSuccess?.(data);
        return data;

      } catch (err) {
        if (!mountedRef.current) return null;

        const error = err as AxiosError;
        
        setState(prev => ({ ...prev, loading: false, error, success: false }));
        
        if (loadingKey) {
          dispatch(setLoading({ key: loadingKey, loading: false }));
        }

        onError?.(error);
        errorHandler.handleError(error, { context: 'useApi', config: finalConfig });
        
        return null;
      }
    }

    return null;
  }, [config, loadingKey, onSuccess, onError, retries, retryDelay, dispatch, cancel]);

  // Execute immediately if requested
  useEffect(() => {
    if (immediate) {
      execute();
    }

    return () => {
      mountedRef.current = false;
      cancel();
    };
  }, [immediate, execute, cancel]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      mountedRef.current = false;
      cancel();
      if (loadingKey) {
        dispatch(setLoading({ key: loadingKey, loading: false }));
      }
    };
  }, [cancel, loadingKey, dispatch]);

  return {
    ...state,
    execute,
    reset,
    cancel,
  };
}

// Specialized hooks for common HTTP methods
export function useGet<T = any>(url: string, options?: UseApiOptions) {
  return useApi<T>({ method: 'GET', url }, options);
}

export function usePost<T = any>(url: string, options?: UseApiOptions) {
  return useApi<T>({ method: 'POST', url }, options);
}

export function usePut<T = any>(url: string, options?: UseApiOptions) {
  return useApi<T>({ method: 'PUT', url }, options);
}

export function useDelete<T = any>(url: string, options?: UseApiOptions) {
  return useApi<T>({ method: 'DELETE', url }, options);
}

// Hook for handling form submissions
export function useFormSubmit<T = any>(
  url: string,
  method: 'POST' | 'PUT' | 'PATCH' = 'POST',
  options?: UseApiOptions
) {
  const api = useApi<T>({ method, url }, options);

  const submit = useCallback((data: any) => {
    return api.execute({ data });
  }, [api]);

  return {
    ...api,
    submit,
  };
}

// Hook for paginated data
export function usePagination<T = any>(
  url: string,
  initialPage = 1,
  pageSize = 10,
  options?: UseApiOptions
) {
  const [page, setPage] = useState(initialPage);
  const [allData, setAllData] = useState<T[]>([]);
  const [hasMore, setHasMore] = useState(true);

  const api = useApi<{ data: T[]; total: number; hasMore: boolean }>(
    {
      method: 'GET',
      url,
      params: { page, limit: pageSize },
    },
    {
      ...options,
      onSuccess: (response) => {
        if (page === 1) {
          setAllData(response.data);
        } else {
          setAllData(prev => [...prev, ...response.data]);
        }
        setHasMore(response.hasMore);
        options?.onSuccess?.(response);
      },
    }
  );

  const loadMore = useCallback(() => {
    if (!api.loading && hasMore) {
      setPage(prev => prev + 1);
    }
  }, [api.loading, hasMore]);

  const refresh = useCallback(() => {
    setPage(1);
    setAllData([]);
    setHasMore(true);
    api.execute();
  }, [api]);

  useEffect(() => {
    api.execute();
  }, [page]);

  return {
    ...api,
    data: allData,
    page,
    hasMore,
    loadMore,
    refresh,
    setPage,
  };
}
