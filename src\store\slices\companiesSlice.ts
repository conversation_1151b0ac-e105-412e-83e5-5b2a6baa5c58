import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { CompaniesState, Organization, ApiError } from '../types';
import {
  fetchOrganizations,
  fetchOrganizationDetails,
  updateOrganizationSettings,
  disconnectOrganization,
  disconnectCompany,
  reconnectCompany,
  refreshOrganizationData
} from '../actions/companies.actions';

// Initial state
const initialState: CompaniesState = {
  organizations: [],
  selectedOrganization: null,
  isLoading: false,
  error: null,
};



// Slice
const companiesSlice = createSlice({
  name: 'companies',
  initialState,
  reducers: {
    setSelectedOrganization: (state, action: PayloadAction<string>) => {
      state.selectedOrganization = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    addOrganization: (state, action: PayloadAction<Organization>) => {
      state.organizations.push(action.payload);
    },
    updateOrganization: (state, action: PayloadAction<{ id: string; updates: Partial<Organization> }>) => {
      const index = state.organizations.findIndex(org => org.id === action.payload.id);
      if (index !== -1) {
        state.organizations[index] = { ...state.organizations[index], ...action.payload.updates };
      }
    },
    removeOrganization: (state, action: PayloadAction<string>) => {
      state.organizations = state.organizations.filter(org => org.id !== action.payload);
      if (state.selectedOrganization === action.payload) {
        state.selectedOrganization = state.organizations.length > 0 ? state.organizations[0].id : null;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch organizations
    builder
      .addCase(fetchOrganizations.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchOrganizations.fulfilled, (state, action) => {
        state.isLoading = false;
        state.organizations = action.payload;
        if (!state.selectedOrganization && action.payload.length > 0) {
          state.selectedOrganization = action.payload[0].id;
        }
      })
      .addCase(fetchOrganizations.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || 'Failed to fetch organizations';
      });

    // Disconnect organization
    builder
      .addCase(disconnectOrganization.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(disconnectOrganization.fulfilled, (state, action) => {
        state.isLoading = false;
        state.organizations = state.organizations.filter(org => org.id !== action.payload.organizationId);
        if (state.selectedOrganization === action.payload.organizationId) {
          state.selectedOrganization = state.organizations.length > 0 ? state.organizations[0].id : null;
        }
      })
      .addCase(disconnectOrganization.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || 'Failed to disconnect organization';
      });

    // Refresh organization data
    builder
      .addCase(refreshOrganizationData.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(refreshOrganizationData.fulfilled, (state, action) => {
        state.isLoading = false;
        const index = state.organizations.findIndex(org => org.id === action.payload.organizationId);
        if (index !== -1) {
          state.organizations[index] = { ...state.organizations[index], ...action.payload.updates };
        }
      })
      .addCase(refreshOrganizationData.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || 'Failed to refresh organization data';
      });

    // Disconnect company
    builder
      .addCase(disconnectCompany.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(disconnectCompany.fulfilled, (state, action) => {
        state.isLoading = false;
        // Update company status to disconnected after successful disconnect
        // Note: We don't have the company ID in the response, so we'll need to track it differently
      })
      .addCase(disconnectCompany.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || 'Failed to disconnect company';
      });

    // Reconnect company
    builder
      .addCase(reconnectCompany.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(reconnectCompany.fulfilled, (state, action) => {
        state.isLoading = false;
        // OAuth URL received, redirect will happen in component
      })
      .addCase(reconnectCompany.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || 'Failed to reconnect company';
      });
  },
});

export const {
  setSelectedOrganization,
  clearError,
  addOrganization,
  updateOrganization,
  removeOrganization
} = companiesSlice.actions;

export default companiesSlice.reducer;
