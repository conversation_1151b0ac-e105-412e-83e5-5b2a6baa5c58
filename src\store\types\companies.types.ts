// Companies-related TypeScript interfaces and types

export interface Organization {
  // Frontend fields (lowercase)
  id?: string;
  name?: string;
  shortCode?: string;
  tenantId?: string;
  connectionId?: string;
  lastSync?: string;
  legalName?: string;
  taxNumber?: string;
  baseCurrency?: string;
  countryCode?: string;
  isDemoCompany?: boolean;
  organizationStatus?: 'ACTIVE' | 'DISCONNECTED' | 'SUSPENDED' | 'PENDING';
  createdDateUTC?: string;
  timezone?: string;
  financialYearEndDay?: number;
  financialYearEndMonth?: number;
  salesTaxBasis?: string;
  salesTaxPeriod?: string;
  version?: string;

  // Backend API response fields (capitalized)
  Id?: string;
  Name?: string;
  XeroTenantId?: string | null;
  ConnectionStatus?: 'ACTIVE' | 'DISCONNECTED' | 'SUSPENDED' | 'PENDING';
  FinancialYearEnd?: string | null;
  CreatedAt?: string;
  UpdatedAt?: string;
  XeroTokenExpiry?: string | null;
}

export interface CompaniesState {
  organizations: Organization[];
  selectedOrganization: string | null;
  isLoading: boolean;
  error: string | null;
}

export interface FetchOrganizationsResponse {
  organizations: Organization[];
}

// Backend API response structure
export interface CompaniesApiResponse {
  companies: {
    Id: string;
    Name: string;
    XeroTenantId: string;
    ConnectionStatus: 'ACTIVE' | 'DISCONNECTED' | 'SUSPENDED' | 'PENDING';
    FinancialYearEnd: string | null;
    CreatedAt: string;
    UpdatedAt: string;
    XeroTokenExpiry: string;
  }[];
  pagination: {
    totalCount: number;
    totalPages: number;
    currentPage: number;
    limit: number;
    offset: number;
    hasNextPage: boolean;
    hasPreviousPage: boolean;
  };
  appliedFilters: {
    limit: number;
    offset: number;
    sortBy: string;
    sortOrder: string;
  };
}
