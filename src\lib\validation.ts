// Runtime validation utilities with TypeScript integration
import { z } from 'zod';

// Base validation schemas
export const emailSchema = z.string().email('Invalid email address');
export const passwordSchema = z.string()
  .min(8, 'Password must be at least 8 characters')
  .regex(/[A-Z]/, 'Password must contain at least one uppercase letter')
  .regex(/[a-z]/, 'Password must contain at least one lowercase letter')
  .regex(/[0-9]/, 'Password must contain at least one number')
  .regex(/[^A-Za-z0-9]/, 'Password must contain at least one special character');

export const phoneSchema = z.string()
  .regex(/^\+?[1-9]\d{1,14}$/, 'Invalid phone number format');

export const urlSchema = z.string().url('Invalid URL format');

// User validation schemas
export const userSchema = z.object({
  id: z.string().uuid(),
  email: emailSchema,
  firstName: z.string().min(1, 'First name is required').max(50, 'First name too long'),
  lastName: z.string().min(1, 'Last name is required').max(50, 'Last name too long'),
  role: z.enum(['admin', 'user', 'viewer']),
  permissions: z.array(z.string()),
  isActive: z.boolean(),
  avatar: z.string().url().optional(),
  lastLogin: z.string().datetime().optional(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

export const loginSchema = z.object({
  email: emailSchema,
  password: z.string().min(1, 'Password is required'),
  rememberMe: z.boolean().optional(),
});

export const registerSchema = z.object({
  email: emailSchema,
  password: passwordSchema,
  firstName: z.string().min(1, 'First name is required').max(50),
  lastName: z.string().min(1, 'Last name is required').max(50),
  acceptTerms: z.boolean().refine(val => val === true, 'You must accept the terms'),
});

export const changePasswordSchema = z.object({
  currentPassword: z.string().min(1, 'Current password is required'),
  newPassword: passwordSchema,
  confirmPassword: z.string(),
}).refine(data => data.newPassword === data.confirmPassword, {
  message: 'Passwords do not match',
  path: ['confirmPassword'],
});

// Company validation schemas
export const addressSchema = z.object({
  street1: z.string().min(1, 'Street address is required'),
  street2: z.string().optional(),
  city: z.string().min(1, 'City is required'),
  state: z.string().min(1, 'State is required'),
  postalCode: z.string().min(1, 'Postal code is required'),
  country: z.string().min(2, 'Country is required').max(2, 'Country code must be 2 characters'),
});

export const companySchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, 'Company name is required').max(100),
  legalName: z.string().max(100).optional(),
  email: emailSchema.optional(),
  phone: phoneSchema.optional(),
  website: urlSchema.optional(),
  address: addressSchema.optional(),
  taxNumber: z.string().max(50).optional(),
  registrationNumber: z.string().max(50).optional(),
  industry: z.string().max(50).optional(),
  size: z.enum(['small', 'medium', 'large', 'enterprise']).optional(),
  isActive: z.boolean(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime(),
});

// Sync entities constants
export const SYNC_ENTITIES = [
  'Accounts',
  'Bank Transactions',
  'Bank Transfers',
  'Budgets',
  'Contacts',
  'Credit Notes',
  'Invoices',
  'Items',
  'Journals',
  'Manual Journals',
  'Payments',
  'Purchase Orders',
  'Quotes',
  'Receipts',
  'Repeating Invoices',
  'Tracking Categories',
  'Tax Rates',
  'Attachments',
  'BalanceSheet',
  'ProfitLoss',
  'TrialBalance'
] as const;

export type SyncEntityType = typeof SYNC_ENTITIES[number];

// Sync priorities
export const SYNC_PRIORITIES = ['HIGH', 'NORMAL', 'LOW'] as const;
export type SyncPriorityType = typeof SYNC_PRIORITIES[number];

// Settings validation schemas
export const syncSettingsSchema = z.object({
  autoSync: z.boolean(),
  syncInterval: z.number().min(5, 'Sync interval must be at least 5 minutes').max(1440, 'Sync interval cannot exceed 24 hours'),
  syncEntities: z.array(z.string()).min(1, 'At least one entity must be selected'),
  conflictResolution: z.enum(['xero_wins', 'local_wins', 'manual']),
  batchSize: z.number().min(1).max(1000),
  retryAttempts: z.number().min(0).max(10),
  retryDelay: z.number().min(1000).max(300000), // 1 second to 5 minutes
  webhookEnabled: z.boolean(),
  webhookUrl: z.string().url().optional(),
  notifications: z.object({
    onSuccess: z.boolean(),
    onFailure: z.boolean(),
    onConflict: z.boolean(),
  }),
});

// Sync trigger request validation schema
export const syncTriggerRequestSchema = z.object({
  companyId: z.string().uuid('Company ID must be a valid UUID').min(1, 'Company ID is required'),
  entities: z
    .array(z.enum(SYNC_ENTITIES))
    .min(1, 'At least one entity must be specified')
    .max(SYNC_ENTITIES.length, `Maximum ${SYNC_ENTITIES.length} entities allowed`)
    .refine(
      (entities) => {
        // Check for duplicates
        const uniqueEntities = new Set(entities);
        return uniqueEntities.size === entities.length;
      },
      {
        message: 'Duplicate entities are not allowed',
      }
    ),
  priority: z.enum(SYNC_PRIORITIES).optional().default('NORMAL'),
  fullSync: z.boolean().optional().default(false),
});

// API validation schemas
export const paginationSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
});

export const sortSchema = z.object({
  field: z.string(),
  order: z.enum(['asc', 'desc']),
});

export const filterSchema = z.object({
  field: z.string(),
  operator: z.enum(['eq', 'ne', 'gt', 'gte', 'lt', 'lte', 'in', 'nin', 'contains', 'startsWith', 'endsWith']),
  value: z.any(),
});

export const queryParamsSchema = z.object({
  page: z.number().min(1).optional(),
  limit: z.number().min(1).max(100).optional(),
  sort: z.array(sortSchema).optional(),
  filters: z.array(filterSchema).optional(),
  search: z.string().optional(),
  include: z.array(z.string()).optional(),
  exclude: z.array(z.string()).optional(),
});

// Validation utility functions
export function validateData<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: T;
  errors?: Record<string, string[]>;
} {
  try {
    const result = schema.parse(data);
    return { success: true, data: result };
  } catch (error) {
    if (error instanceof z.ZodError) {
      const errors: Record<string, string[]> = {};

      error.errors.forEach((err) => {
        const path = err.path.join('.');
        if (!errors[path]) {
          errors[path] = [];
        }
        errors[path].push(err.message);
      });

      return { success: false, errors };
    }

    return {
      success: false,
      errors: { _general: ['Validation failed'] }
    };
  }
}

export function validatePartialData<T>(schema: z.ZodSchema<T>, data: unknown): {
  success: boolean;
  data?: Partial<T>;
  errors?: Record<string, string[]>;
} {
  return validateData(schema.partial(), data);
}

// Type-safe form validation hook
export function createFormValidator<T>(schema: z.ZodSchema<T>) {
  return {
    validate: (data: unknown) => validateData(schema, data),
    validatePartial: (data: unknown) => validatePartialData(schema, data),
    validateField: (field: keyof T, value: unknown) => {
      try {
        const fieldSchema = schema.shape[field as string];
        if (fieldSchema) {
          fieldSchema.parse(value);
          return { success: true };
        }
        return { success: false, error: 'Field not found' };
      } catch (error) {
        if (error instanceof z.ZodError) {
          return {
            success: false,
            error: error.errors[0]?.message || 'Validation failed'
          };
        }
        return { success: false, error: 'Validation failed' };
      }
    },
    schema,
  };
}

// Common validators
export const validators = {
  user: createFormValidator(userSchema),
  login: createFormValidator(loginSchema),
  register: createFormValidator(registerSchema),
  changePassword: createFormValidator(changePasswordSchema),
  company: createFormValidator(companySchema),
  syncSettings: createFormValidator(syncSettingsSchema),
  syncTriggerRequest: createFormValidator(syncTriggerRequestSchema),
  queryParams: createFormValidator(queryParamsSchema),
};

// Runtime type checking utilities
export function isValidEmail(email: string): boolean {
  return emailSchema.safeParse(email).success;
}

export function isValidPassword(password: string): boolean {
  return passwordSchema.safeParse(password).success;
}

export function isValidUrl(url: string): boolean {
  return urlSchema.safeParse(url).success;
}

export function isValidUuid(uuid: string): boolean {
  return z.string().uuid().safeParse(uuid).success;
}

// Additional validation functions for form components
export function validateEmail(email: string): { isValid: boolean; error?: string } {
  const result = emailSchema.safeParse(email);
  return {
    isValid: result.success,
    error: result.success ? undefined : result.error.errors[0]?.message
  };
}

export function validatePassword(password: string): {
  isValid: boolean;
  error?: string;
  strength?: {
    score: number;
    level: 'weak' | 'fair' | 'good' | 'strong' | 'very-strong';
    feedback: string[];
    checks: {
      length: boolean;
      uppercase: boolean;
      lowercase: boolean;
      numbers: boolean;
      symbols: boolean;
      noRepeats: boolean;
    };
  };
} {
  const result = passwordSchema.safeParse(password);

  // Calculate password strength
  const strength = customValidators.strongPassword(password);
  const checks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    numbers: /[0-9]/.test(password),
    symbols: /[^A-Za-z0-9]/.test(password),
    noRepeats: !/(.)\1{2,}/.test(password),
  };

  const getStrengthLevel = (score: number): 'weak' | 'fair' | 'good' | 'strong' | 'very-strong' => {
    if (score <= 1) return 'weak';
    if (score <= 2) return 'fair';
    if (score <= 3) return 'good';
    if (score <= 4) return 'strong';
    return 'very-strong';
  };

  return {
    isValid: result.success,
    error: result.success ? undefined : result.error.errors[0]?.message,
    strength: {
      score: strength.score,
      level: getStrengthLevel(strength.score),
      feedback: strength.feedback,
      checks,
    },
  };
}

export function validateName(name: string): { isValid: boolean; error?: string } {
  const nameSchema = z.string().min(1, 'Name is required').max(50, 'Name too long');
  const result = nameSchema.safeParse(name);
  return {
    isValid: result.success,
    error: result.success ? undefined : result.error.errors[0]?.message
  };
}

// Dedicated password strength checker for registration
export function checkPasswordStrength(password: string): {
  score: number;
  level: 'weak' | 'fair' | 'good' | 'strong' | 'very-strong';
  percentage: number;
  feedback: string[];
  checks: {
    length: boolean;
    uppercase: boolean;
    lowercase: boolean;
    numbers: boolean;
    symbols: boolean;
    noRepeats: boolean;
  };
  color: string;
  description: string;
} {
  const strength = customValidators.strongPassword(password);
  const checks = {
    length: password.length >= 8,
    uppercase: /[A-Z]/.test(password),
    lowercase: /[a-z]/.test(password),
    numbers: /[0-9]/.test(password),
    symbols: /[^A-Za-z0-9]/.test(password),
    noRepeats: !/(.)\1{2,}/.test(password),
  };

  const getStrengthLevel = (score: number): 'weak' | 'fair' | 'good' | 'strong' | 'very-strong' => {
    if (score <= 1) return 'weak';
    if (score <= 2) return 'fair';
    if (score <= 3) return 'good';
    if (score <= 4) return 'strong';
    return 'very-strong';
  };

  const getStrengthColor = (level: string): string => {
    switch (level) {
      case 'weak': return 'bg-red-500';
      case 'fair': return 'bg-orange-500';
      case 'good': return 'bg-yellow-500';
      case 'strong': return 'bg-blue-500';
      case 'very-strong': return 'bg-green-500';
      default: return 'bg-gray-300';
    }
  };

  const getStrengthDescription = (level: string): string => {
    switch (level) {
      case 'weak': return 'Very weak password';
      case 'fair': return 'Fair password';
      case 'good': return 'Good password';
      case 'strong': return 'Strong password';
      case 'very-strong': return 'Very strong password';
      default: return 'Password strength unknown';
    }
  };

  const level = getStrengthLevel(strength.score);
  const percentage = Math.min(100, (strength.score / 5) * 100);

  return {
    score: strength.score,
    level,
    percentage,
    feedback: strength.feedback,
    checks,
    color: getStrengthColor(level),
    description: getStrengthDescription(level),
  };
}

// Custom validation rules
export const customValidators = {
  uniqueEmail: async (email: string, excludeId?: string): Promise<boolean> => {
    // This would typically make an API call to check uniqueness
    // For now, return true as a placeholder
    return true;
  },

  strongPassword: (password: string): { isValid: boolean; score: number; feedback: string[] } => {
    const feedback: string[] = [];
    let score = 0;

    if (password.length >= 8) score += 1;
    else feedback.push('Use at least 8 characters');

    if (/[A-Z]/.test(password)) score += 1;
    else feedback.push('Include uppercase letters');

    if (/[a-z]/.test(password)) score += 1;
    else feedback.push('Include lowercase letters');

    if (/[0-9]/.test(password)) score += 1;
    else feedback.push('Include numbers');

    if (/[^A-Za-z0-9]/.test(password)) score += 1;
    else feedback.push('Include special characters');

    if (password.length >= 12) score += 1;
    if (/(.)\1{2,}/.test(password)) score -= 1; // Repeated characters

    return {
      isValid: score >= 4,
      score: Math.max(0, Math.min(5, score)),
      feedback,
    };
  },

  businessEmail: (email: string): boolean => {
    const freeEmailDomains = [
      'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com',
      'aol.com', 'icloud.com', 'protonmail.com'
    ];

    if (!isValidEmail(email)) return false;

    const domain = email.split('@')[1]?.toLowerCase();
    return !freeEmailDomains.includes(domain);
  },
};

// Export types for TypeScript integration
export type UserData = z.infer<typeof userSchema>;
export type LoginData = z.infer<typeof loginSchema>;
export type RegisterData = z.infer<typeof registerSchema>;
export type ChangePasswordData = z.infer<typeof changePasswordSchema>;
export type CompanyData = z.infer<typeof companySchema>;
export type SyncSettingsData = z.infer<typeof syncSettingsSchema>;
export type SyncTriggerRequestData = z.infer<typeof syncTriggerRequestSchema>;
export type QueryParamsData = z.infer<typeof queryParamsSchema>;
export type ValidationResult<T> = ReturnType<typeof validateData<T>>;
export type FormValidator<T> = ReturnType<typeof createFormValidator<T>>;
