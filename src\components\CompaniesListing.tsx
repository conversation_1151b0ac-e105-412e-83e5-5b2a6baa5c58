import React, { useState, useEffect, useMemo, useCallback } from "react";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  fetchOrganizations,
  disconnectOrganization,
  reconnectCompany,
} from "@/store/actions/companies.actions";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Unplug,
  Search,
  RefreshCw,
  Building,
  Calendar,
  DollarSign,
  Link,
} from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { LoadingSpinner } from "@/components/common/LoadingSpinner";
import { EmptyState } from "@/components/common/EmptyState";

interface CompaniesListingProps {
  onBack?: () => void;
}

const CompaniesListing: React.FC<CompaniesListingProps> = ({ onBack }) => {
  const dispatch = useAppDispatch();
  const { organizations, isLoading, error } = useAppSelector(
    (state) => state.companies
  );
  const { isConnected } = useAppSelector((state) => state.auth);

  const [searchQuery, setSearchQuery] = useState("");
  const [isRefreshing, setIsRefreshing] = useState(false);

  // Fetch organizations on component mount - only if not already loaded
  useEffect(() => {
    if (isConnected && organizations.length === 0 && !isLoading) {
      dispatch(fetchOrganizations());
    }
  }, [dispatch, isConnected, organizations.length, isLoading]);

  // Filtered organizations based on search
  const filteredOrganizations = useMemo(() => {
    if (!searchQuery) return organizations;

    return organizations.filter(
      (org) =>
        org.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        org.shortCode.toLowerCase().includes(searchQuery.toLowerCase())
    );
  }, [organizations, searchQuery]);

  // Enhanced refresh handler
  const handleRefresh = useCallback(async () => {
    if (!isConnected || isRefreshing) return;

    setIsRefreshing(true);
    try {
      await dispatch(fetchOrganizations()).unwrap();
      toast.success("Organizations refreshed successfully", { duration: 2000 });
    } catch (error) {
      toast.error("Failed to refresh organizations", { duration: 3000 });
    } finally {
      setIsRefreshing(false);
    }
  }, [dispatch, isConnected, isRefreshing]);

  // Enhanced disconnect handler with confirmation
  const handleDisconnect = useCallback(
    async (organizationId: string, organizationName: string) => {
      try {
        await (dispatch as any)(
          disconnectOrganization(organizationId)
        ).unwrap();

        toast.success(`Disconnected from ${organizationName}`, {
          duration: 3000,
        });

        // Refresh companies list to show updated status
        try {
          await (dispatch as any)(fetchOrganizations()).unwrap();
          toast.success("Company list updated", {
            duration: 2000,
          });
        } catch (fetchError) {
          console.warn(
            "Failed to refresh companies after disconnect:",
            fetchError
          );
          toast.info(
            "Disconnect successful, please refresh to see updated status",
            {
              duration: 3000,
            }
          );
        }
      } catch (error) {
        toast.error(`Failed to disconnect from ${organizationName}`, {
          duration: 3000,
        });
      }
    },
    [dispatch]
  );

  // Handle reconnect organization - calls API to get OAuth URL then redirects
  const handleReconnect = useCallback(
    async (organizationId: string, organizationName: string) => {
      try {
        toast.info(`Initiating reconnection for ${organizationName}...`, {
          duration: 3000,
        });

        // Call reconnect Redux action to get OAuth URL
        const result = await (dispatch as any)(
          reconnectCompany(organizationId)
        ).unwrap();

        // Redirect to the OAuth URL received from API
        if (result.data.authorizationUrl) {
          window.location.href = result.data.authorizationUrl;
          toast.success(`Redirecting to Xero for ${organizationName}...`, {
            duration: 2000,
          });
        } else {
          throw new Error("No OAuth URL received from server");
        }
      } catch (error: any) {
        console.error("Reconnect failed:", error);
        toast.error(
          `Failed to initiate reconnection for ${organizationName}: ${error.message}`,
          {
            duration: 5000,
          }
        );
      }
    },
    [dispatch]
  );

  // Loading state
  if (isLoading && organizations.length === 0) {
    return (
      <Card className="w-full">
        <CardContent className="flex items-center justify-center py-12">
          <LoadingSpinner text="Loading organizations..." />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div>
            <CardTitle className="text-xl text-gray-800">
              Connected Organizations
            </CardTitle>
            <p className="text-sm text-gray-600 mt-1">
              Manage your connected Xero organizations
            </p>
          </div>
          <div className="flex items-center gap-3">
            <Badge
              variant="secondary"
              className="bg-emerald-100 text-emerald-700"
            >
              {organizations.length} Connected
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={handleRefresh}
              disabled={isRefreshing || !isConnected}
              className="flex items-center gap-2"
            >
              <RefreshCw
                className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`}
              />
              {isRefreshing ? "Refreshing..." : "Refresh"}
            </Button>
          </div>
        </div>

        {/* Search Bar */}
        {organizations.length > 0 && (
          <div className="relative max-w-md mt-4">
            <Search className="absolute left-3 top-2.5 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Search organizations..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 border-amber-200 focus:ring-amber-500"
            />
          </div>
        )}
      </CardHeader>

      <CardContent>
        {filteredOrganizations.length > 0 ? (
          <div className="space-y-4">
            {filteredOrganizations.map((organization) => (
              <div
                key={organization.Id || organization.id}
                className="flex items-center justify-between p-4 border rounded-lg hover:bg-muted/50 transition-colors border-emerald-200"
              >
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <div className="w-8 h-8 bg-emerald-100 rounded-full flex items-center justify-center">
                      <Building className="h-4 w-4 text-emerald-600" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-800">
                        {organization.name || organization.name}
                      </div>
                      <div className="text-sm text-muted-foreground">
                        {organization.tenantId ? (
                          <>ID: {organization.tenantId}</>
                        ) : (
                          ""
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                <div className="flex items-center gap-4">
                  <div className="flex items-center gap-2">
                    {/* Status indicator based on ConnectionStatus */}
                    {(() => {
                      const status =
                        (organization as any).ConnectionStatus ||
                        (organization as any).organizationStatus ||
                        "DISCONNECTED";
                      const isConnected = status === "ACTIVE";
                      const isPending = status === "PENDING";
                      const isDisconnected = status === "Disconnected";

                      return (
                        <>
                          <div
                            className={`w-2 h-2 rounded-full ${
                              isConnected
                                ? "bg-emerald-500 animate-pulse"
                                : isPending
                                ? "bg-amber-500 animate-pulse"
                                : "bg-red-500"
                            }`}
                          ></div>
                          <Badge
                            variant="secondary"
                            className={
                              isConnected
                                ? "bg-emerald-100 text-emerald-700"
                                : isPending
                                ? "bg-amber-100 text-amber-700"
                                : "bg-red-100 text-red-700"
                            }
                          >
                            {isConnected
                              ? "Connected"
                              : isPending
                              ? "Pending"
                              : "Disconnected"}
                          </Badge>
                        </>
                      );
                    })()}
                  </div>

                  {/* Conditional Action Button based on ConnectionStatus */}
                  {(() => {
                    const status =
                      (organization as any).ConnectionStatus ||
                      (organization as any).organizationStatus ||
                      "DISCONNECTED";
                    const isConnected = status === "ACTIVE";
                    const orgId = (organization as any).Id || organization.id;
                    const orgName =
                      (organization as any).Name || organization.name;

                    if (isConnected) {
                      // Show Disconnect button for connected organizations
                      return (
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-2 text-red-600 border-red-200 hover:bg-red-50"
                            >
                              <Unplug className="h-4 w-4" />
                              Disconnect
                            </Button>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>
                                Disconnect Organization
                              </AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to disconnect from "
                                {orgName}"? This will stop all data
                                synchronization and remove access to this
                                organization's data.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDisconnect(orgId, orgName)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Disconnect
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      );
                    } else {
                      // Show Reconnect button for disconnected organizations
                      return (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleReconnect(orgId, orgName)}
                          className="flex items-center gap-2 text-emerald-600 border-emerald-200 hover:bg-emerald-50"
                        >
                          <Link className="h-4 w-4" />
                          Reconnect
                        </Button>
                      );
                    }
                  })()}
                </div>
              </div>
            ))}
          </div>
        ) : organizations.length > 0 ? (
          // No search results
          <EmptyState
            icon={Search}
            title="No Organizations Found"
            description={`No organizations match "${searchQuery}". Try adjusting your search.`}
            action={{
              label: "Clear Search",
              onClick: () => setSearchQuery(""),
              variant: "outline" as const,
            }}
          />
        ) : (
          // No organizations at all
          <EmptyState
            icon={Building}
            title="No Organizations Connected"
            description="Connect to Xero to see your organizations here and start syncing data."
          />
        )}

        {onBack && (
          <div className="mt-6 pt-4 border-t">
            <Button
              variant="outline"
              onClick={onBack}
              className="hover:bg-amber-50 border-amber-200"
            >
              Back to Integration
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default React.memo(CompaniesListing);
