import React, { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { loginUser, fetchUserProfile } from "@/store/actions/auth.actions";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import { 
  Loader2, 
  Mail, 
  Lock, 
  LogIn, 
  Eye, 
  EyeOff, 
  Shield,
  CheckCircle,
  AlertCircle
} from "lucide-react";
import { toast } from "@/components/ui/sonner";
import { useFormSubmit } from "@/hooks/useApi";
import { validateEmail, validatePassword } from "@/lib/validation";

interface LoginFormData {
  email: string;
  password: string;
  rememberMe: boolean;
}

interface FormErrors {
  email: string;
  password: string;
  general: string;
}

const Login: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [formData, setFormData] = useState<LoginFormData>({
    email: "",
    password: "",
    rememberMe: false,
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({
    email: "",
    password: "",
    general: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [loginAttempts, setLoginAttempts] = useState(0);

  // Enhanced form validation with real-time feedback
  const validateForm = useCallback((): boolean => {
    const errors: FormErrors = {
      email: "",
      password: "",
      general: "",
    };

    // Email validation
    if (!formData.email) {
      errors.email = "Email is required";
    } else {
      const emailValidation = validateEmail(formData.email);
      if (!emailValidation.isValid) {
        errors.email = emailValidation.error || "Invalid email format";
      }
    }

    // Password validation
    if (!formData.password) {
      errors.password = "Password is required";
    } else {
      const passwordValidation = validatePassword(formData.password);
      if (!passwordValidation.isValid) {
        errors.password = passwordValidation.error || "Password is too weak";
      }
    }

    setFormErrors(errors);
    return !errors.email && !errors.password && !errors.general;
  }, [formData]);

  // Real-time validation
  const fieldValidation = useMemo(() => {
    return {
      email: {
        isValid: !formErrors.email && formData.email.length > 0,
        isEmpty: formData.email.length === 0,
      },
      password: {
        isValid: !formErrors.password && formData.password.length > 0,
        isEmpty: formData.password.length === 0,
      }
    };
  }, [formErrors, formData]);

  // Enhanced input change handler with debounced validation
  const handleInputChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    
    setFormData((prev) => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));

    // Clear specific field error when user starts typing
    if (formErrors[name as keyof FormErrors]) {
      setFormErrors((prev) => ({
        ...prev,
        [name]: "",
        general: "", // Clear general errors too
      }));
    }
  }, [formErrors]);

  // Enhanced form submission with better error handling
  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();

    // Rate limiting for login attempts
    if (loginAttempts >= 5) {
      toast.error("Too many login attempts", {
        description: "Please wait a few minutes before trying again.",
        duration: 5000,
      });
      return;
    }

    if (!validateForm()) {
      toast.error("Please fix the form errors", {
        description: "Check your email and password.",
        duration: 3000,
      });
      return;
    }

    try {
      // Increment login attempts
      setLoginAttempts(prev => prev + 1);

      const loginData = {
        email: formData.email.toLowerCase().trim(),
        password: formData.password,
        rememberMe: formData.rememberMe,
      };

      await dispatch(loginUser(loginData)).unwrap();

      // Fetch user profile after successful login
      try {
        await dispatch(fetchUserProfile()).unwrap();
      } catch (profileError) {
        console.warn("Failed to fetch user profile:", profileError);
        // Don't block login flow if profile fetch fails
      }

      // Reset login attempts on success
      setLoginAttempts(0);

      // Show success toast
      toast.success("Welcome back!", {
        description: "You have successfully logged in.",
        duration: 3000,
      });

      // Navigate to dashboard
      navigate("/dashboard");
    } catch (error: any) {
      console.error("Login failed:", error);
      
      // Enhanced error handling
      let errorMessage = "Please check your credentials and try again.";
      
      if (error?.message?.includes("Invalid credentials")) {
        errorMessage = "Invalid email or password. Please try again.";
      } else if (error?.message?.includes("Account locked")) {
        errorMessage = "Your account has been temporarily locked. Please contact support.";
      } else if (error?.message?.includes("Network")) {
        errorMessage = "Network error. Please check your connection and try again.";
      }

      setFormErrors(prev => ({
        ...prev,
        general: typeof error === "string" ? error : errorMessage,
      }));

      toast.error("Login failed", {
        description: errorMessage,
        duration: 4000,
      });
    }
  }, [formData, validateForm, dispatch, navigate, loginAttempts]);

  // Password strength indicator
  const getPasswordStrength = useCallback((password: string): number => {
    if (!password) return 0;
    let strength = 0;
    if (password.length >= 8) strength += 25;
    if (/[A-Z]/.test(password)) strength += 25;
    if (/[0-9]/.test(password)) strength += 25;
    if (/[^A-Za-z0-9]/.test(password)) strength += 25;
    return strength;
  }, []);

  const passwordStrength = getPasswordStrength(formData.password);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-amber-50 to-orange-100 p-4">
      <Card className="w-full max-w-md shadow-xl border-0 animate-in slide-in-from-bottom-4 duration-500">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-amber-500 rounded-full flex items-center justify-center shadow-lg">
              <LogIn className="w-6 h-6 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            Welcome Back
          </CardTitle>
          <CardDescription className="text-center">
            Sign in to your account to continue
          </CardDescription>
        </CardHeader>

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            {/* General Error Alert */}
            {(error || formErrors.general) && (
              <Alert variant="destructive" className="animate-in slide-in-from-top-2">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {formErrors.general || error}
                </AlertDescription>
              </Alert>
            )}

            {/* Login Attempts Warning */}
            {loginAttempts >= 3 && (
              <Alert className="border-yellow-200 bg-yellow-50">
                <Shield className="h-4 w-4 text-yellow-600" />
                <AlertDescription className="text-yellow-800">
                  {5 - loginAttempts} attempts remaining before temporary lockout
                </AlertDescription>
              </Alert>
            )}

            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                Email Address
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="Enter your email"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`pl-10 transition-all duration-200 ${
                    formErrors.email 
                      ? "border-red-500 focus:ring-red-500" 
                      : fieldValidation.email.isValid 
                        ? "border-green-500 focus:ring-green-500"
                        : ""
                  }`}
                  disabled={isLoading}
                  autoComplete="email"
                  required
                />
                {fieldValidation.email.isValid && (
                  <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
                )}
              </div>
              {formErrors.email && (
                <p className="text-sm text-red-500 animate-in slide-in-from-top-1">
                  {formErrors.email}
                </p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium">
                Password
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Enter your password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`pl-10 pr-10 transition-all duration-200 ${
                    formErrors.password 
                      ? "border-red-500 focus:ring-red-500" 
                      : fieldValidation.password.isValid 
                        ? "border-green-500 focus:ring-green-500"
                        : ""
                  }`}
                  disabled={isLoading}
                  autoComplete="current-password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                  tabIndex={-1}
                >
                  {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                </button>
              </div>
              
              {/* Password Strength Indicator */}
              {formData.password && (
                <div className="space-y-1">
                  <Progress value={passwordStrength} className="h-1" />
                  <p className="text-xs text-gray-500">
                    Password strength: {
                      passwordStrength < 25 ? "Weak" :
                      passwordStrength < 50 ? "Fair" :
                      passwordStrength < 75 ? "Good" : "Strong"
                    }
                  </p>
                </div>
              )}
              
              {formErrors.password && (
                <p className="text-sm text-red-500 animate-in slide-in-from-top-1">
                  {formErrors.password}
                </p>
              )}
            </div>

            {/* Remember Me */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="rememberMe"
                name="rememberMe"
                checked={formData.rememberMe}
                onCheckedChange={(checked) => 
                  setFormData(prev => ({ ...prev, rememberMe: checked as boolean }))
                }
                disabled={isLoading}
              />
              <Label htmlFor="rememberMe" className="text-sm text-gray-600">
                Remember me for 30 days
              </Label>
            </div>
          </CardContent>

          <CardFooter className="flex flex-col space-y-4">
            <Button
              type="submit"
              className="w-full bg-amber-500 hover:bg-amber-600 transition-all duration-200 shadow-lg hover:shadow-xl"
              disabled={isLoading || loginAttempts >= 5}
              size="lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Signing in...
                </>
              ) : (
                <>
                  <LogIn className="mr-2 h-4 w-4" />
                  Sign In
                </>
              )}
            </Button>

            <div className="text-center text-sm text-gray-600">
              Don't have an account?{" "}
              <Link
                to="/register"
                className="text-amber-600 hover:text-amber-700 font-medium transition-colors"
              >
                Sign up
              </Link>
            </div>

            {/* Forgot Password Link */}
            <div className="text-center">
              <Link
                to="/forgot-password"
                className="text-sm text-gray-500 hover:text-gray-700 transition-colors"
              >
                Forgot your password?
              </Link>
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default React.memo(Login);
