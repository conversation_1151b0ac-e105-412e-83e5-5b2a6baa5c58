import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  User, 
  Mail, 
  RefreshCw, 
  CheckCircle, 
  XCircle,
  Clock,
  AlertTriangle 
} from 'lucide-react';
import { useAppDispatch, useAppSelector } from '@/store/hooks';
import { fetchUserProfile } from '@/store/actions/auth.actions';
import { toast } from '@/components/ui/sonner';

const UserProfileTest: React.FC = () => {
  const dispatch = useAppDispatch();
  const { user, isLoading, error, isAuthenticated } = useAppSelector((state) => state.auth);

  const handleFetchProfile = async () => {
    try {
      await dispatch(fetchUserProfile()).unwrap();
      toast.success("Profile updated!", {
        description: "User profile has been refreshed successfully.",
        duration: 3000,
      });
    } catch (error) {
      console.error("Failed to fetch profile:", error);
      toast.error("Profile update failed", {
        description: typeof error === 'string' ? error : "Failed to fetch user profile",
        duration: 4000,
      });
    }
  };

  const getStatusBadge = () => {
    if (!isAuthenticated) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <XCircle className="w-3 h-3" />
          Not Authenticated
        </Badge>
      );
    }

    if (isLoading) {
      return (
        <Badge variant="secondary" className="flex items-center gap-1">
          <Clock className="w-3 h-3 animate-spin" />
          Loading...
        </Badge>
      );
    }

    if (error) {
      return (
        <Badge variant="destructive" className="flex items-center gap-1">
          <AlertTriangle className="w-3 h-3" />
          Error
        </Badge>
      );
    }

    if (user) {
      return (
        <Badge variant="default" className="flex items-center gap-1 bg-green-500">
          <CheckCircle className="w-3 h-3" />
          Profile Loaded
        </Badge>
      );
    }

    return (
      <Badge variant="outline" className="flex items-center gap-1">
        <AlertTriangle className="w-3 h-3" />
        No Profile Data
      </Badge>
    );
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between">
          <span className="flex items-center gap-2">
            <User className="w-5 h-5" />
            User Profile Test
          </span>
          {getStatusBadge()}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Authentication Status */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Authentication:</span>
          <span className="text-sm text-gray-600">
            {isAuthenticated ? "✅ Authenticated" : "❌ Not Authenticated"}
          </span>
        </div>

        {/* User Information */}
        {user ? (
          <div className="space-y-3 p-3 bg-gray-50 rounded-lg">
            <div className="flex items-center gap-2">
              <User className="w-4 h-4 text-gray-500" />
              <div>
                <div className="font-medium text-sm">{user.name}</div>
                <div className="text-xs text-gray-500">Full Name</div>
              </div>
            </div>
            
            <div className="flex items-center gap-2">
              <Mail className="w-4 h-4 text-gray-500" />
              <div>
                <div className="font-medium text-sm">{user.email}</div>
                <div className="text-xs text-gray-500">Email Address</div>
              </div>
            </div>

            {user.id && (
              <div className="flex items-center gap-2">
                <div className="w-4 h-4 bg-gray-400 rounded text-xs text-white flex items-center justify-center font-mono">
                  ID
                </div>
                <div>
                  <div className="font-medium text-sm font-mono">{user.id}</div>
                  <div className="text-xs text-gray-500">User ID</div>
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="p-3 bg-gray-50 rounded-lg text-center text-gray-500">
            {isLoading ? (
              <div className="flex items-center justify-center gap-2">
                <RefreshCw className="w-4 h-4 animate-spin" />
                Loading profile...
              </div>
            ) : error ? (
              <div className="text-red-500">
                <AlertTriangle className="w-4 h-4 mx-auto mb-1" />
                <div className="text-sm">{error}</div>
              </div>
            ) : (
              <div>
                <User className="w-8 h-8 mx-auto mb-2 text-gray-400" />
                <div className="text-sm">No profile data available</div>
              </div>
            )}
          </div>
        )}

        {/* Actions */}
        <div className="space-y-2">
          <Button
            onClick={handleFetchProfile}
            disabled={isLoading || !isAuthenticated}
            variant="outline"
            size="sm"
            className="w-full"
          >
            <RefreshCw className={`w-4 h-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
            {isLoading ? 'Fetching...' : 'Refresh Profile'}
          </Button>

          {error && (
            <div className="text-xs text-red-500 bg-red-50 p-2 rounded">
              <strong>Error Details:</strong> {error}
            </div>
          )}
        </div>

        {/* Debug Info */}
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
          <div><strong>Auth Token:</strong> {localStorage.getItem('auth_token') ? 'Present' : 'Missing'}</div>
          <div><strong>Loading State:</strong> {isLoading ? 'True' : 'False'}</div>
          <div><strong>User Object:</strong> {user ? 'Present' : 'Null'}</div>
          <div><strong>Error State:</strong> {error || 'None'}</div>
        </div>
      </CardContent>
    </Card>
  );
};

export default UserProfileTest;
