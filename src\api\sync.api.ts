// Sync Management API Service
// Handles all sync-related API calls for data synchronization

import { apiClient, handleApiError } from './axios.config';

/**
 * Sync status for a single entity
 */
export interface EntitySyncStatus {
  entity: string;
  lastSyncAt?: string;
  status: 'idle' | 'syncing' | 'success' | 'error';
  recordCount?: number;
  errorMessage?: string;
}

/**
 * Overall sync status response
 */
export interface SyncStatusResponse {
  companyId: string;
  entities: EntitySyncStatus[];
  lastFullSyncAt?: string;
  isAnySyncing: boolean;
}

/**
 * Sync trigger request parameters
 */
export interface SyncTriggerRequest {
  companyId: string;
  entities: string[];
  priority?: 'HIGH' | 'NORMAL' | 'LOW';
  fullSync?: boolean;
  force?: boolean; // Keep for backward compatibility
}

/**
 * Sync trigger response
 */
export interface SyncTriggerResponse {
  success: boolean;
  message: string;
  jobId?: string;
  estimatedDuration?: number;
}

/**
 * Sync history item
 */
export interface SyncHistoryItem {
  id: string;
  companyId: string;
  entity: string;
  status: 'pending' | 'running' | 'completed' | 'failed';
  startedAt: string;
  completedAt?: string;
  recordsProcessed?: number;
  errorMessage?: string;
  duration?: number;
}

/**
 * Sync history response with pagination
 */
export interface SyncHistoryResponse {
  items: SyncHistoryItem[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

/**
 * Supported sync entity
 */
export interface SyncEntity {
  name: string;
  displayName: string;
  description: string;
  isEnabled: boolean;
  estimatedDuration?: number;
  dependencies?: string[];
}

/**
 * Sync history query parameters
 */
export interface SyncHistoryFilters {
  companyId: string;
  limit?: number;
  offset?: number;
  entity?: string;
  status?: string;
  dateFrom?: string;
  dateTo?: string;
}

/**
 * Sync Management API service
 */
export const syncApi = {
  /**
   * Get sync status for all entities
   * GET /api/v1/sync/status
   */
  getStatus: async (companyId: string): Promise<SyncStatusResponse> => {
    try {
      const response = await apiClient.get<SyncStatusResponse>('/sync/status', {
        params: { companyId },
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Trigger sync for specific entities
   * POST /api/v1/sync/trigger
   */
  triggerSync: async (request: SyncTriggerRequest): Promise<SyncTriggerResponse> => {
    try {
      const response = await apiClient.post<SyncTriggerResponse>('/sync/trigger', request);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Trigger sync for all entities
   * POST /api/v1/sync/trigger-all
   */
  triggerAllSync: async (companyId: string, force?: boolean): Promise<SyncTriggerResponse> => {
    try {
      const response = await apiClient.post<SyncTriggerResponse>('/sync/trigger-all', {
        companyId,
        force,
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Get sync history with filtering
   * GET /api/v1/sync/history
   */
  getHistory: async (filters: SyncHistoryFilters): Promise<SyncHistoryResponse> => {
    try {
      const response = await apiClient.get<SyncHistoryResponse>('/sync/history', {
        params: filters,
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Get supported entities list
   * GET /api/v1/sync/entities
   */
  getEntities: async (): Promise<SyncEntity[]> => {
    try {
      const response = await apiClient.get<SyncEntity[]>('/sync/entities');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },
};

export default syncApi;
