// Sync Logs Page - Synchronization history and monitoring
// Displays sync logs, status, and detailed sync information

import React, { useCallback } from "react";
import {
  SidebarProvider,
  SidebarTrigger,
  SidebarInset,
} from "@/components/ui/sidebar";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { Building, RefreshCw } from "lucide-react";
import { AppSidebar } from "@/components/AppSidebar";
import SyncLogs from "@/components/SyncLogs";
import { useOrganizations } from "@/hooks/useOrganizations";
import { SyncLoadingOverlay } from "@/components/common/PageLoadingOverlay";

/**
 * Sync Logs Page Component
 *
 * Synchronization monitoring interface providing:
 * - Detailed sync history and logs
 * - Real-time sync status monitoring
 * - Error tracking and troubleshooting
 * - Sync performance analytics
 *
 * @returns {JSX.Element} The complete sync logs interface
 */
const SyncLogsPage: React.FC = () => {
  // Use custom hook for organizations management
  const {
    organizations,
    selectedOrganization,
    isLoading: companiesLoading,
    connectedOrganizations,
    refreshOrganizations,
    selectOrganization,
  } = useOrganizations({
    autoLoad: true,
    autoSelect: true,
    showErrorToasts: true,
    showSuccessToasts: true,
  });

  /**
   * Handle organization selection change
   */
  const handleOrganizationChange = useCallback(
    (organizationId: string) => {
      selectOrganization(organizationId);
    },
    [selectOrganization]
  );

  /**
   * Handle manual refresh
   */
  const handleRefresh = useCallback(async () => {
    try {
      await refreshOrganizations();
    } catch (error) {
      // Error handling is done in the hook
    }
  }, [refreshOrganizations]);

  return (
    <SidebarProvider>
      <div className="min-h-screen flex w-full bg-amber-50 relative">
        <AppSidebar />
        <SidebarInset className="flex-1 flex flex-col min-w-0 relative">
          {/* Header */}
          <header className="flex h-20 shrink-0 items-center gap-4 border-b border-amber-200 bg-white/95 backdrop-blur-sm px-8 shadow-sm">
            <SidebarTrigger className="-ml-1 hover:bg-amber-50 transition-colors" />
            <div className="flex-1">
              <div className="flex items-center gap-3 mb-1">
                <span className="text-2xl">📋</span>
                <h1 className="text-2xl font-bold text-gray-800">Sync Logs</h1>
              </div>
              <p className="text-sm text-gray-600">
                Monitor synchronization history and troubleshoot issues
              </p>
            </div>
            {organizations.length > 0 && (
              <div className="flex items-center gap-4">
                <Select
                  value={selectedOrganization || ""}
                  onValueChange={handleOrganizationChange}
                  disabled={companiesLoading}
                >
                  <SelectTrigger className="w-64 bg-white border-amber-200 hover:border-amber-300 focus:border-amber-400">
                    <div className="flex items-center gap-2">
                      <Building className="h-4 w-4 text-amber-600" />
                      <SelectValue placeholder="Select organization..." />
                    </div>
                  </SelectTrigger>
                  <SelectContent>
                    {connectedOrganizations.map((org) => (
                      <SelectItem key={org.id} value={org.id}>
                        <div className="flex flex-col gap-1 py-1">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">{org.name}</span>
                            <div className="w-2 h-2 bg-emerald-500 rounded-full"></div>
                          </div>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <Button
                  onClick={handleRefresh}
                  disabled={companiesLoading}
                  variant="outline"
                  size="sm"
                  className="border-amber-200 hover:border-amber-300 hover:bg-amber-50"
                >
                  <RefreshCw
                    className={`h-4 w-4 mr-2 ${
                      companiesLoading ? "animate-spin" : ""
                    }`}
                  />
                  Refresh
                </Button>
                <div className="flex items-center gap-2 px-3 py-1.5 bg-emerald-50 border border-emerald-200 rounded-full">
                  <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                  <span className="text-sm font-medium text-emerald-700">
                    Connected
                  </span>
                </div>
              </div>
            )}
          </header>

          {/* Main Content */}
          <main className="flex-1 overflow-auto p-8 bg-amber-50 relative">
            <div className="max-w-7xl mx-auto">
              <SyncLogs />
            </div>

            {/* Loading Overlay */}
            <SyncLoadingOverlay isLoading={companiesLoading} operation="logs" />
          </main>
        </SidebarInset>
      </div>
    </SidebarProvider>
  );
};

export default React.memo(SyncLogsPage);
