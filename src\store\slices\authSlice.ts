import { createSlice, PayloadAction } from '@reduxjs/toolkit';
import { AuthState, CompanyInfo, User } from '../types/auth.types';
import {
  loginUser,
  registerUser,
  logoutUser,
  checkAuthStatus,
  fetchUserProfile,
  initiateXeroOAuth,
  completeXeroOAuth,
  refreshAuthToken,
  validateSession,
  disconnectXero,
  refreshAccessToken
} from '../actions/auth.actions';

// Initial state
const initialState: AuthState = {
  // User authentication
  isAuthenticated: !!localStorage.getItem('auth_token'),
  user: null,
  token: localStorage.getItem('auth_token'),

  // Xero integration
  isConnected: false,
  isLoading: false,
  error: null,
  oauthUrl: null,
  companyInfo: null,
  accessToken: null,
  refreshToken: null,
};





// Slice
const authSlice = createSlice({
  name: 'auth',
  initialState,
  reducers: {
    clearError: (state) => {
      state.error = null;
    },
    updateCompanyInfo: (state, action: PayloadAction<Partial<CompanyInfo>>) => {
      if (state.companyInfo) {
        state.companyInfo = { ...state.companyInfo, ...action.payload };
      }
    },
    setTokens: (state, action: PayloadAction<{ accessToken: string; refreshToken: string }>) => {
      state.accessToken = action.payload.accessToken;
      state.refreshToken = action.payload.refreshToken;
    },
  },
  extraReducers: (builder) => {
    // User Authentication

    // Login
    builder
      .addCase(loginUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(loginUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload.user;
        state.token = action.payload.token;
        state.error = null;
      })
      .addCase(loginUser.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.error = action.payload as string || 'Login failed';
      });

    // Register
    builder
      .addCase(registerUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(registerUser.fulfilled, (state, action) => {
        state.isLoading = false;
        // Don't authenticate user after registration - they should login manually
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.error = null;
      })
      .addCase(registerUser.rejected, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.error = action.payload as string || 'Registration failed';
      });

    // Logout
    builder
      .addCase(logoutUser.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(logoutUser.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.isConnected = false;
        state.accessToken = null;
        state.refreshToken = null;
        state.companyInfo = null;
        state.error = null;
      })
      .addCase(logoutUser.rejected, (state, action) => {
        state.isLoading = false;
        // Even if logout fails on server, clear local state for security
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
        state.isConnected = false;
        state.accessToken = null;
        state.refreshToken = null;
        state.companyInfo = null;
        state.error = action.payload as string || 'Logout failed';
      });

    // Check Auth Status
    builder
      .addCase(checkAuthStatus.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(checkAuthStatus.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isAuthenticated = true;
        state.user = action.payload;
        state.error = null;
      })
      .addCase(checkAuthStatus.rejected, (state) => {
        state.isLoading = false;
        state.isAuthenticated = false;
        state.user = null;
        state.token = null;
      });

    // Fetch User Profile
    builder
      .addCase(fetchUserProfile.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchUserProfile.fulfilled, (state, action) => {
        state.isLoading = false;
        state.user = action.payload;
        state.error = null;
      })
      .addCase(fetchUserProfile.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || 'Failed to fetch user profile';
      });

    // Xero Integration

    // Initiate OAuth
    builder
      .addCase(initiateXeroOAuth.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(initiateXeroOAuth.fulfilled, (state) => {
        state.isLoading = false;
      })
      .addCase(initiateXeroOAuth.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || 'Failed to initiate OAuth';
      });

    // Complete OAuth
    builder
      .addCase(completeXeroOAuth.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(completeXeroOAuth.fulfilled, (state, action) => {
        state.isLoading = false;
        state.isConnected = true;
        state.accessToken = action.payload.accessToken;
        state.refreshToken = action.payload.refreshToken;
        state.companyInfo = action.payload.companyInfo;
      })
      .addCase(completeXeroOAuth.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || 'Failed to complete OAuth';
      });

    // Refresh token
    builder
      .addCase(refreshAccessToken.fulfilled, (state, action) => {
        state.accessToken = action.payload.accessToken;
        state.refreshToken = action.payload.refreshToken;
      })
      .addCase(refreshAccessToken.rejected, (state, action) => {
        state.error = action.payload as string || 'Failed to refresh token';
        // If refresh fails, user needs to re-authenticate
        state.isConnected = false;
        state.accessToken = null;
        state.refreshToken = null;
      });

    // Disconnect
    builder
      .addCase(disconnectXero.pending, (state) => {
        state.isLoading = true;
      })
      .addCase(disconnectXero.fulfilled, (state) => {
        state.isLoading = false;
        state.isConnected = false;
        state.accessToken = null;
        state.refreshToken = null;
        state.companyInfo = null;
        state.error = null;
      })
      .addCase(disconnectXero.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload as string || 'Failed to disconnect';
      });
  },
});

export const { clearError, updateCompanyInfo, setTokens } = authSlice.actions;
export default authSlice.reducer;
