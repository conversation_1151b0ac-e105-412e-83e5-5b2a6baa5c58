import { createAsyncThunk, createSlice, PayloadAction } from '@reduxjs/toolkit';
import { SyncLogsState, SyncLog, ApiError } from '../types';
import {
  fetchSyncLogs as fetchSyncLogsAction,
  retrySyncOperation,
  clearSyncLogs,
  exportSyncLogs
} from '../actions/syncLogs.actions';

// Mock data
const mockSyncLogs: SyncLog[] = [
  {
    id: '1',
    timestamp: '2024-06-12 10:30:45',
    endpoint: 'Accounts',
    status: 'success',
    duration: '2.3min',
    message: 'Successfully synced all accounts',
    responseBody: '{"accounts": [{"id": "123", "name": "Sample Account", "type": "BANK"}], "total": 45}',
    requestId: 'c743aafb-a157-4dd1-8105-803089b90e84',
    fullUrl: 'http://localhost:8081/dev/accounts'
  },
  {
    id: '2',
    timestamp: '2024-06-12 10:25:12',
    endpoint: 'Bank Transactions',
    status: 'success',
    duration: '5.7min',
    message: 'Bank transactions synced successfully',
    responseBody: '{"transactions": [{"id": "456", "amount": 1500.00, "type": "RECEIVE"}], "total": 128}',
    requestId: 'b852ccfb-b267-4ee2-9206-914190c01f95',
    fullUrl: 'http://localhost:8081/dev/bank-transactions'
  },
  {
    id: '3',
    timestamp: '2024-06-12 10:20:33',
    endpoint: 'Invoices',
    status: 'warning',
    duration: '4.2min',
    message: '3 invoices had validation warnings',
    responseBody: '{"invoices": [{"id": "789", "warnings": ["Missing tax code"]}], "total": 89}',
    requestId: 'd954eefg-c378-4ff3-a317-a25201d02g06',
    fullUrl: 'http://localhost:8081/dev/invoices'
  },
  {
    id: '4',
    timestamp: '2024-06-12 09:45:21',
    endpoint: 'Payments',
    status: 'error',
    duration: '1.1min',
    message: 'API rate limit exceeded',
    responseBody: '{"error": "Rate limit exceeded", "retry_after": 300}',
    requestId: 'e065ffgh-d489-5gg4-b428-b36312e03h17',
    fullUrl: 'http://localhost:8081/dev/payments'
  }
];

// Initial state
const initialState: SyncLogsState = {
  logs: mockSyncLogs,
  filteredLogs: mockSyncLogs,
  filters: {
    startDate: '', // No default date
    endDate: '',   // No default date
    endpoint: 'all',
    status: 'all',
    searchQuery: '',
  },
  selectedLog: null,
  isLoading: false,
  error: null,
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    hasNext: false,
    hasPrev: false,
  },
};

// Note: fetchSyncLogs is imported from actions/syncLogs.actions.ts

// Note: retrySyncOperation is imported from actions/syncLogs.actions.ts

// Note: Filtering is now handled by the API, not locally

// Slice
const syncLogsSlice = createSlice({
  name: 'syncLogs',
  initialState,
  reducers: {
    setFilters: (state, action: PayloadAction<Partial<SyncLogsState['filters']>>) => {
      state.filters = { ...state.filters, ...action.payload };
      // Note: filteredLogs will be updated by API response, not local filtering
      // Reset pagination when filters change
      state.pagination.page = 1;
    },
    setSelectedLog: (state, action: PayloadAction<SyncLog | null>) => {
      state.selectedLog = action.payload;
    },
    clearError: (state) => {
      state.error = null;
    },
    addSyncLog: (state, action: PayloadAction<SyncLog>) => {
      state.logs.unshift(action.payload); // Add to beginning
      state.filteredLogs.unshift(action.payload); // Add to filtered logs as well
    },
    clearLogs: (state) => {
      state.logs = [];
      state.filteredLogs = [];
      state.selectedLog = null;
      state.pagination = {
        page: 1,
        limit: 20,
        total: 0,
        hasNext: false,
        hasPrev: false,
      };
    },
    setPagination: (state, action: PayloadAction<{ page?: number; limit?: number }>) => {
      if (action.payload.page !== undefined) {
        state.pagination.page = action.payload.page;
      }
      if (action.payload.limit !== undefined) {
        state.pagination.limit = action.payload.limit;
      }
    },
  },
  extraReducers: (builder) => {
    // Fetch sync logs
    builder
      .addCase(fetchSyncLogsAction.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(fetchSyncLogsAction.fulfilled, (state, action: any) => {
        state.isLoading = false;
        // Handle API response with pagination
        state.logs = action.payload.syncLogs;
        state.filteredLogs = action.payload.items;
        state.pagination = action.payload.pagination;
      })
      .addCase(fetchSyncLogsAction.rejected, (state, action) => {
        state.isLoading = false;
        state.error = action.payload || 'Failed to fetch sync logs';
      });

    // Retry sync operation
    builder
      .addCase(retrySyncOperation.pending, (state) => {
        state.isLoading = true;
        state.error = null;
      })
      .addCase(retrySyncOperation.fulfilled, (state) => {
        state.isLoading = false;
        // The retry operation returns success info, not a new log
        // We should refresh the logs instead
      })
      .addCase(retrySyncOperation.rejected, (state, action) => {
        state.isLoading = false;
        state.error = (action.payload as string) || 'Failed to retry sync operation';
      });
  },
});

export const {
  setFilters,
  setSelectedLog,
  clearError,
  addSyncLog,
  clearLogs,
  setPagination
} = syncLogsSlice.actions;

export default syncLogsSlice.reducer;
