// Comprehensive API type definitions
import { AxiosError } from 'axios';

// Base API response structure
export interface ApiResponse<T = any> {
  success: boolean;
  data: T;
  message?: string;
  errors?: string[];
  meta?: {
    page?: number;
    limit?: number;
    total?: number;
    totalPages?: number;
  };
}

// Error response structure
export interface ApiErrorResponse {
  success: false;
  message: string;
  errors?: string[];
  code?: string;
  details?: Record<string, any>;
}

// Paginated response
export interface PaginatedResponse<T> {
  data: T[];
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
}

// API request configuration
export interface ApiRequestConfig {
  skipAuth?: boolean;
  skipCache?: boolean;
  timeout?: number;
  retries?: number;
  loadingKey?: string;
}

// User types
export interface User {
  id: string;
  email: string;
  firstName: string;
  lastName: string;
  fullName: string;
  role: 'admin' | 'user' | 'viewer';
  permissions: string[];
  avatar?: string;
  isActive: boolean;
  lastLogin?: string;
  createdAt: string;
  updatedAt: string;
}

export interface UserProfile extends User {
  preferences: {
    theme: 'light' | 'dark' | 'system';
    language: string;
    timezone: string;
    notifications: {
      email: boolean;
      push: boolean;
      sms: boolean;
    };
  };
}

// Authentication types
export interface LoginRequest {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface LoginResponse {
  user: User;
  token: string;
  refreshToken: string;
  expiresIn: number;
}

export interface RegisterRequest {
  email: string;
  password: string;
  firstName: string;
  lastName: string;
  acceptTerms: boolean;
}

export interface RefreshTokenRequest {
  refreshToken: string;
}

export interface ChangePasswordRequest {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

// Xero integration types
export interface XeroConnection {
  id: string;
  tenantId: string;
  tenantName: string;
  tenantType: string;
  isConnected: boolean;
  accessToken?: string;
  refreshToken?: string;
  expiresAt?: string;
  scopes: string[];
  connectedAt: string;
  lastSyncAt?: string;
}

export interface XeroOAuthRequest {
  code: string;
  state?: string;
}

export interface XeroOAuthResponse {
  connection: XeroConnection;
  organizations: XeroOrganization[];
}

export interface XeroOrganization {
  organisationID: string;
  name: string;
  legalName: string;
  paysTax: boolean;
  version: string;
  organisationType: string;
  baseCurrency: string;
  countryCode: string;
  isDemoCompany: boolean;
  organisationStatus: string;
  registrationNumber?: string;
  taxNumber?: string;
  financialYearEndDay: number;
  financialYearEndMonth: number;
  salesTaxBasis: string;
  salesTaxPeriod: string;
  defaultSalesTax?: string;
  defaultPurchasesTax?: string;
  periodLockDate?: string;
  endOfYearLockDate?: string;
  createdDateUTC: string;
  timezone: string;
  organisationEntityType: string;
  shortCode: string;
  lineOfBusiness?: string;
}

// Company/Organization types
export interface Company {
  id: string;
  name: string;
  legalName?: string;
  email?: string;
  phone?: string;
  website?: string;
  address?: Address;
  taxNumber?: string;
  registrationNumber?: string;
  industry?: string;
  size?: 'small' | 'medium' | 'large' | 'enterprise';
  isActive: boolean;
  xeroConnection?: XeroConnection;
  createdAt: string;
  updatedAt: string;
}

export interface Address {
  street1: string;
  street2?: string;
  city: string;
  state: string;
  postalCode: string;
  country: string;
}

// Sync logs types
export interface SyncLog {
  id: string;
  companyId: string;
  companyName: string;
  entityType: string;
  operation: 'create' | 'update' | 'delete' | 'sync';
  status: 'pending' | 'running' | 'completed' | 'failed' | 'cancelled';
  startedAt: string;
  completedAt?: string;
  duration?: number;
  recordsProcessed: number;
  recordsSucceeded: number;
  recordsFailed: number;
  errors?: SyncError[];
  metadata?: Record<string, any>;
  createdBy: string;
  createdAt: string;
}

export interface SyncError {
  id: string;
  recordId?: string;
  recordType?: string;
  errorCode: string;
  errorMessage: string;
  details?: Record<string, any>;
  timestamp: string;
}

export interface SyncStats {
  totalSyncs: number;
  successfulSyncs: number;
  failedSyncs: number;
  successRate: number;
  averageDuration: number;
  lastSyncAt?: string;
  nextSyncAt?: string;
}

// Settings types
export interface AppSettings {
  id: string;
  companyId?: string;
  category: 'general' | 'sync' | 'notifications' | 'security' | 'integration';
  key: string;
  value: any;
  type: 'string' | 'number' | 'boolean' | 'object' | 'array';
  description?: string;
  isEditable: boolean;
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SyncSettings {
  autoSync: boolean;
  syncInterval: number; // in minutes
  syncEntities: string[];
  conflictResolution: 'xero_wins' | 'local_wins' | 'manual';
  batchSize: number;
  retryAttempts: number;
  retryDelay: number;
  webhookEnabled: boolean;
  webhookUrl?: string;
  notifications: {
    onSuccess: boolean;
    onFailure: boolean;
    onConflict: boolean;
  };
}

// Notification types
export interface Notification {
  id: string;
  userId: string;
  type: 'info' | 'success' | 'warning' | 'error';
  title: string;
  message: string;
  data?: Record<string, any>;
  isRead: boolean;
  isArchived: boolean;
  expiresAt?: string;
  createdAt: string;
  updatedAt: string;
}

// Webhook types
export interface Webhook {
  id: string;
  companyId: string;
  url: string;
  events: string[];
  isActive: boolean;
  secret?: string;
  headers?: Record<string, string>;
  retryAttempts: number;
  lastTriggeredAt?: string;
  lastStatus?: 'success' | 'failed';
  createdAt: string;
  updatedAt: string;
}

export interface WebhookEvent {
  id: string;
  webhookId: string;
  event: string;
  payload: Record<string, any>;
  status: 'pending' | 'sent' | 'failed' | 'retrying';
  attempts: number;
  lastAttemptAt?: string;
  nextAttemptAt?: string;
  response?: {
    status: number;
    body: string;
    headers: Record<string, string>;
  };
  createdAt: string;
}

// Report types
export interface Report {
  id: string;
  name: string;
  description?: string;
  type: 'sync_summary' | 'error_analysis' | 'performance' | 'usage';
  parameters: Record<string, any>;
  schedule?: {
    frequency: 'daily' | 'weekly' | 'monthly';
    time: string;
    timezone: string;
    recipients: string[];
  };
  isActive: boolean;
  lastGeneratedAt?: string;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface ReportData {
  reportId: string;
  generatedAt: string;
  data: Record<string, any>;
  charts?: ChartData[];
  tables?: TableData[];
  summary?: Record<string, any>;
}

export interface ChartData {
  type: 'line' | 'bar' | 'pie' | 'doughnut' | 'area';
  title: string;
  data: any[];
  options?: Record<string, any>;
}

export interface TableData {
  title: string;
  headers: string[];
  rows: any[][];
  pagination?: {
    page: number;
    limit: number;
    total: number;
  };
}

// Utility types
export type ApiMethod = 'GET' | 'POST' | 'PUT' | 'PATCH' | 'DELETE';

export type SortOrder = 'asc' | 'desc';

export interface SortConfig {
  field: string;
  order: SortOrder;
}

export interface FilterConfig {
  field: string;
  operator: 'eq' | 'ne' | 'gt' | 'gte' | 'lt' | 'lte' | 'in' | 'nin' | 'contains' | 'startsWith' | 'endsWith';
  value: any;
}

export interface QueryParams {
  page?: number;
  limit?: number;
  sort?: SortConfig[];
  filters?: FilterConfig[];
  search?: string;
  include?: string[];
  exclude?: string[];
}

// Type guards
export function isApiError(error: any): error is AxiosError<ApiErrorResponse> {
  return error?.response?.data?.success === false;
}

export function isApiResponse<T>(response: any): response is ApiResponse<T> {
  return typeof response === 'object' && 'success' in response;
}

export function isPaginatedResponse<T>(response: any): response is PaginatedResponse<T> {
  return response && 'data' in response && 'pagination' in response;
}
