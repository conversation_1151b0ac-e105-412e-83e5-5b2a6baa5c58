// Enhanced Axios configuration for API calls

import axios, { AxiosError, AxiosRequestConfig, AxiosResponse } from 'axios';
import { isTokenExpired, clearAuthTokens } from '@/lib/auth.utils';
import { errorHand<PERSON>, ErrorCode } from '@/lib/error-handler';

// API configuration with environment-specific settings
const API_CONFIG = {
  baseURL: import.meta.env.VITE_API_BASE_URL || 'http://localhost:9000/api/v1',
  timeout: 30000, // 30 seconds timeout
  retryAttempts: 3,
  retryDelay: 1000, // 1 second base delay
  cacheTimeout: 5 * 60 * 1000, // 5 minutes cache

  // CORS and credentials configuration
  withCredentials: true,

  // Environment-specific settings
  isDevelopment: import.meta.env.DEV,
  isProduction: import.meta.env.PROD,

  // API versioning
  apiVersion: 'v1',
  clientVersion: '1.0.0',
};

// Request cache for GET requests
const requestCache = new Map<string, { data: any; timestamp: number }>();

// Retry queue for failed requests
const retryQueue: Array<() => Promise<any>> = [];

// Create axios instance with base configuration
export const apiClient = axios.create({
  baseURL: API_CONFIG.baseURL,
  timeout: API_CONFIG.timeout,
  withCredentials: API_CONFIG.withCredentials, // Enable credentials for CORS
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Accept-Language': 'en-US,en;q=0.9',

    'Cache-Control': 'no-cache',
    'Pragma': 'no-cache',
  },
  // Additional axios configuration for better error handling
  validateStatus: (status) => {
    // Consider 2xx and 3xx as successful, handle 4xx and 5xx as errors
    return status >= 200 && status < 400;
  },
});

// Utility functions
const getCacheKey = (config: AxiosRequestConfig): string => {
  return `${config.method?.toUpperCase()}_${config.url}_${JSON.stringify(config.params || {})}`;
};

const shouldCache = (config: AxiosRequestConfig): boolean => {
  return config.method?.toLowerCase() === 'get' &&
    !config.url?.includes('/auth/') &&
    !(config as any).skipCache;
};

const getFromCache = (key: string): any | null => {
  const cached = requestCache.get(key);
  if (cached && Date.now() - cached.timestamp < API_CONFIG.cacheTimeout) {
    return cached.data;
  }
  requestCache.delete(key);
  return null;
};

const setCache = (key: string, data: any): void => {
  requestCache.set(key, { data, timestamp: Date.now() });
};

// Enhanced request interceptor
apiClient.interceptors.request.use(
  (config) => {
    // Add request metadata
    (config as any).metadata = {
      requestId: `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
      startTime: Date.now(),
    };

    // Check cache for GET requests
    if (shouldCache(config)) {
      const cacheKey = getCacheKey(config);
      const cachedData = getFromCache(cacheKey);
      if (cachedData) {
        // Return cached response (this won't work directly, but we'll handle it in response interceptor)
        (config as any).cachedData = cachedData;
      }
    }

    // Define endpoints that should NOT include auth token
    const publicEndpoints = ['/user/login', '/user/register'];
    const isPublicEndpoint = publicEndpoints.some(endpoint =>
      config.url?.includes(endpoint)
    );

    // Add authentication headers
    if (!isPublicEndpoint) {
      const authToken = localStorage.getItem('auth_token');

      if (authToken && !isTokenExpired(authToken)) {
        config.headers!.Authorization = `Bearer ${authToken}`;
      } else if (authToken && isTokenExpired(authToken)) {

        clearAuthTokens();

        if (!window.location.pathname.includes('/login')) {
          window.location.href = '/login';
        }

        return Promise.reject(new Error('Authentication token expired'));
      }

      // For Xero-specific endpoints, add Xero access token
      const xeroToken = localStorage.getItem('xero_access_token');
      if (xeroToken && config.url?.includes('/xero/')) {
        config.headers!['X-Xero-Token'] = xeroToken;
      }
    }

    // Add tracking and custom headers (matching CORS allowedHeaders)
    config.headers!['X-Request-ID'] = (config as any).metadata.requestId;
    config.headers!['X-Correlation-ID'] = `corr_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
    config.headers!['X-Client-Version'] = '1.0.0';
    config.headers!['X-Client-Platform'] = 'web';

    // Add API key if available (for protected endpoints)
    const apiKey = import.meta.env.VITE_API_KEY;
    if (apiKey && !isPublicEndpoint) {
      config.headers!['X-API-Key'] = apiKey;
    }

    // Add forwarded headers for tracking
    config.headers!['X-Forwarded-For'] = 'client';


    return config;
  },
  (error) => {
    errorHandler.handleError(error, { context: 'request_interceptor' });
    return Promise.reject(error);
  }
);

// Enhanced response interceptor with retry logic and caching
apiClient.interceptors.response.use(
  (response) => {
    const config = response.config as any;
    const duration = Date.now() - (config.metadata?.startTime || Date.now());

    // Extract and log correlation ID from response headers (exposed by CORS)
    const correlationId = response.headers['x-correlation-id'];
    const rateLimitRemaining = response.headers['x-rate-limit-remaining'];
    const rateLimitReset = response.headers['x-rate-limit-reset'];

    // Log performance metrics with correlation tracking


    // Cache successful GET responses
    if (shouldCache(config) && response.status === 200) {
      const cacheKey = getCacheKey(config);
      setCache(cacheKey, response.data);
    }

    // Store correlation ID for request tracking
    if (correlationId) {
      (response as any).correlationId = correlationId;
    }

    return response;
  },
  async (error: AxiosError) => {
    const config = error.config as any;
    const duration = Date.now() - (config?.metadata?.startTime || Date.now());

    // Enhanced error logging with CORS-specific information
    const errorInfo = {
      requestId: config?.metadata?.requestId,
      correlationId: config?.headers?.['X-Correlation-ID'],
      status: error.response?.status,
      statusText: error.response?.statusText,
      url: config?.url,
      method: config?.method,
      duration,
      data: error.response?.data,
      origin: window.location.origin,
      isCorsError: !error.response && error.message.includes('Network Error'),
    };

    console.error('❌ API Error:', errorInfo);

    // Handle CORS-specific errors
    if (errorInfo.isCorsError) {
      console.error('🚫 CORS Error detected:', {
        message: 'Request blocked by CORS policy',
        origin: errorInfo.origin,
        targetUrl: config?.baseURL + config?.url,
        suggestion: 'Check if the backend CORS configuration allows this origin',
      });
    }

    // Handle specific error cases with retry logic
    if (error.response?.status === 401) {
      // Unauthorized - clear tokens and redirect
      clearAuthTokens();


      if (!window.location.pathname.includes('/login')) {
        window.location.href = '/login';
      }

      return Promise.reject(error);
    }

    // Retry logic for certain errors
    const shouldRetry = (
      !config._retry &&
      config.method?.toLowerCase() !== 'post' && // Don't retry POST requests
      (
        error.response?.status === 429 || // Rate limit
        error.response?.status >= 500 || // Server errors
        error.code === 'NETWORK_ERROR' ||
        error.code === 'ECONNABORTED'
      )
    );

    if (shouldRetry) {
      config._retry = (config._retry || 0) + 1;

      if (config._retry <= API_CONFIG.retryAttempts) {
        const delay = API_CONFIG.retryDelay * Math.pow(2, config._retry - 1); // Exponential backoff



        await new Promise(resolve => setTimeout(resolve, delay));
        return apiClient(config);
      }
    }

    // Handle rate limiting
    if (error.response?.status === 429) {
      const retryAfter = error.response.headers['retry-after'];
      if (retryAfter) {

      }
    }

    // Use centralized error handler
    errorHandler.handleError(error, {
      context: 'api_response',
      requestId: config?.metadata?.requestId,
      url: config?.url,
      method: config?.method,
      status: error.response?.status,
    });

    return Promise.reject(error);
  }
);

// Enhanced API utilities and helpers

// Request wrapper with built-in error handling
export const apiRequest = async <T = any>(
  config: AxiosRequestConfig
): Promise<{ data: T; error: null } | { data: null; error: AxiosError }> => {
  try {
    const response = await apiClient(config);
    return { data: response.data, error: null };
  } catch (error) {
    return { data: null, error: error as AxiosError };
  }
};

// Typed API methods
export const api = {
  get: <T = any>(url: string, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'GET', url }),

  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'POST', url, data }),

  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'PUT', url, data }),

  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'PATCH', url, data }),

  delete: <T = any>(url: string, config?: AxiosRequestConfig) =>
    apiRequest<T>({ ...config, method: 'DELETE', url }),
};

// Enhanced error handling utilities
export const handleApiError = (error: AxiosError): string => {
  const response = error.response;

  if (response?.data) {
    const data = response.data as any;
    if (data.message) return data.message;
    if (data.error) return data.error;
    if (data.details) return data.details;
  }

  if (error.message) return error.message;
  return 'An unexpected error occurred';
};

export const getErrorCode = (error: AxiosError): ErrorCode => {
  if (!error.response) {
    if (error.code === 'NETWORK_ERROR') return ErrorCode.NETWORK_ERROR;
    if (error.code === 'ECONNABORTED') return ErrorCode.TIMEOUT_ERROR;
    return ErrorCode.CONNECTION_ERROR;
  }

  const status = error.response.status;
  switch (status) {
    case 401: return ErrorCode.AUTH_UNAUTHORIZED;
    case 403: return ErrorCode.AUTH_FORBIDDEN;
    case 404: return ErrorCode.API_NOT_FOUND;
    case 422: return ErrorCode.API_VALIDATION_ERROR;
    case 429: return ErrorCode.API_SERVER_ERROR; // Rate limit
    case 500:
    case 502:
    case 503:
    case 504: return ErrorCode.API_SERVER_ERROR;
    default: return ErrorCode.UNKNOWN_ERROR;
  }
};

// Utility functions
export const isNetworkError = (error: AxiosError): boolean => {
  return !error.response && (
    error.code === 'NETWORK_ERROR' ||
    error.code === 'ERR_NETWORK'
  );
};

export const isTimeoutError = (error: AxiosError): boolean => {
  return error.code === 'ECONNABORTED' ||
    error.message?.includes('timeout') ||
    error.code === 'ETIMEDOUT';
};

export const isRetryableError = (error: AxiosError): boolean => {
  if (isNetworkError(error) || isTimeoutError(error)) return true;

  const status = error.response?.status;
  return status === 429 || (status !== undefined && status >= 500);
};

// Cache management utilities
export const clearApiCache = (): void => {
  requestCache.clear();
};

export const getCacheStats = () => {
  return {
    size: requestCache.size,
    keys: Array.from(requestCache.keys()),
  };
};

// Request cancellation utilities
export const createCancelToken = () => {
  const source = axios.CancelToken.source();
  return {
    token: source.token,
    cancel: source.cancel,
  };
};

export const isRequestCancelled = (error: any): boolean => {
  return axios.isCancel(error);
};

// CORS debugging utilities
export const getCorsInfo = () => {
  return {
    origin: window.location.origin,
    baseURL: API_CONFIG.baseURL,
    withCredentials: API_CONFIG.withCredentials,
    isDevelopment: API_CONFIG.isDevelopment,
    clientVersion: API_CONFIG.clientVersion,
  };
};



// Create request with custom headers for specific endpoints
export const createRequestWithHeaders = (additionalHeaders: Record<string, string>) => {
  return axios.create({
    ...apiClient.defaults,
    headers: {
      ...apiClient.defaults.headers,
      ...additionalHeaders,
    },
  });
};

export default apiClient;
