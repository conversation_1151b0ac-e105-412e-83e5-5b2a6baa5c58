// Settings-related TypeScript interfaces and types

export interface SyncEntity {
  name: string;
  lastSync: string;
  status: 'success' | 'pending' | 'syncing' | 'error';
  recordCount?: number;
  enabled: boolean;
  syncFrequency: 'manual' | 'hourly' | 'daily' | 'weekly';
}

export interface SettingsState {
  entities: SyncEntity[];
  isLoading: boolean;
  error: string | null;
  lastSyncAll: string | null;
}

export interface FetchSyncSettingsResponse {
  entities: SyncEntity[];
  lastSyncAll: string | null;
}

export interface SyncEntityResponse {
  success: boolean;
  entity?: SyncEntity;
  error?: string;
}

export interface SyncAllEntitiesResponse {
  success: boolean;
  entities?: SyncEntity[];
  lastSyncAll?: string;
  error?: string;
}
