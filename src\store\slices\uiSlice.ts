import { createSlice, PayloadAction } from '@reduxjs/toolkit';

export interface NotificationState {
  id: string;
  type: 'success' | 'error' | 'warning' | 'info';
  title: string;
  message: string;
  duration?: number;
  timestamp: number;
}

export interface LoadingState {
  [key: string]: boolean;
}

export interface UIState {
  // Loading states for different operations
  loading: LoadingState;
  
  // Global notifications
  notifications: NotificationState[];
  
  // Modal states
  modals: {
    [key: string]: boolean;
  };
  
  // Sidebar state
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;
  
  // Theme and appearance
  theme: 'light' | 'dark' | 'system';
  
  // Layout preferences
  layout: {
    compactMode: boolean;
    showSidebar: boolean;
    sidebarWidth: number;
  };
  
  // Feature flags
  features: {
    [key: string]: boolean;
  };
  
  // User preferences
  preferences: {
    language: string;
    timezone: string;
    dateFormat: string;
    currency: string;
  };
}

const initialState: UIState = {
  loading: {},
  notifications: [],
  modals: {},
  sidebarOpen: true,
  sidebarCollapsed: false,
  theme: 'system',
  layout: {
    compactMode: false,
    showSidebar: true,
    sidebarWidth: 280,
  },
  features: {
    darkMode: true,
    notifications: true,
    analytics: false,
  },
  preferences: {
    language: 'en',
    timezone: Intl.DateTimeFormat().resolvedOptions().timeZone,
    dateFormat: 'MM/dd/yyyy',
    currency: 'USD',
  },
};

const uiSlice = createSlice({
  name: 'ui',
  initialState,
  reducers: {
    // Loading states
    setLoading: (state, action: PayloadAction<{ key: string; loading: boolean }>) => {
      state.loading[action.payload.key] = action.payload.loading;
    },
    
    clearLoading: (state, action: PayloadAction<string>) => {
      delete state.loading[action.payload];
    },
    
    clearAllLoading: (state) => {
      state.loading = {};
    },

    // Notifications
    addNotification: (state, action: PayloadAction<Omit<NotificationState, 'id' | 'timestamp'>>) => {
      const notification: NotificationState = {
        ...action.payload,
        id: `notification_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        timestamp: Date.now(),
      };
      state.notifications.push(notification);
    },
    
    removeNotification: (state, action: PayloadAction<string>) => {
      state.notifications = state.notifications.filter(n => n.id !== action.payload);
    },
    
    clearNotifications: (state) => {
      state.notifications = [];
    },

    // Modals
    openModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = true;
    },
    
    closeModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = false;
    },
    
    toggleModal: (state, action: PayloadAction<string>) => {
      state.modals[action.payload] = !state.modals[action.payload];
    },

    // Sidebar
    setSidebarOpen: (state, action: PayloadAction<boolean>) => {
      state.sidebarOpen = action.payload;
    },
    
    toggleSidebar: (state) => {
      state.sidebarOpen = !state.sidebarOpen;
    },
    
    setSidebarCollapsed: (state, action: PayloadAction<boolean>) => {
      state.sidebarCollapsed = action.payload;
    },
    
    toggleSidebarCollapsed: (state) => {
      state.sidebarCollapsed = !state.sidebarCollapsed;
    },

    // Theme
    setTheme: (state, action: PayloadAction<'light' | 'dark' | 'system'>) => {
      state.theme = action.payload;
    },

    // Layout
    updateLayout: (state, action: PayloadAction<Partial<UIState['layout']>>) => {
      state.layout = { ...state.layout, ...action.payload };
    },
    
    setCompactMode: (state, action: PayloadAction<boolean>) => {
      state.layout.compactMode = action.payload;
    },
    
    setSidebarWidth: (state, action: PayloadAction<number>) => {
      state.layout.sidebarWidth = Math.max(200, Math.min(400, action.payload));
    },

    // Features
    toggleFeature: (state, action: PayloadAction<string>) => {
      state.features[action.payload] = !state.features[action.payload];
    },
    
    setFeature: (state, action: PayloadAction<{ key: string; enabled: boolean }>) => {
      state.features[action.payload.key] = action.payload.enabled;
    },

    // Preferences
    updatePreferences: (state, action: PayloadAction<Partial<UIState['preferences']>>) => {
      state.preferences = { ...state.preferences, ...action.payload };
    },
    
    setLanguage: (state, action: PayloadAction<string>) => {
      state.preferences.language = action.payload;
    },
    
    setTimezone: (state, action: PayloadAction<string>) => {
      state.preferences.timezone = action.payload;
    },
    
    setDateFormat: (state, action: PayloadAction<string>) => {
      state.preferences.dateFormat = action.payload;
    },
    
    setCurrency: (state, action: PayloadAction<string>) => {
      state.preferences.currency = action.payload;
    },

    // Reset state
    resetUI: (state) => {
      return { ...initialState, preferences: state.preferences };
    },
  },
});

export const {
  setLoading,
  clearLoading,
  clearAllLoading,
  addNotification,
  removeNotification,
  clearNotifications,
  openModal,
  closeModal,
  toggleModal,
  setSidebarOpen,
  toggleSidebar,
  setSidebarCollapsed,
  toggleSidebarCollapsed,
  setTheme,
  updateLayout,
  setCompactMode,
  setSidebarWidth,
  toggleFeature,
  setFeature,
  updatePreferences,
  setLanguage,
  setTimezone,
  setDateFormat,
  setCurrency,
  resetUI,
} = uiSlice.actions;

export default uiSlice.reducer;
