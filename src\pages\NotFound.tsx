import React, { useEffect } from "react";
import { useLocation, useNavigate, <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Home, ArrowLeft, Search, HelpCircle } from "lucide-react";

const NotFound: React.FC = () => {
  const location = useLocation();
  const navigate = useNavigate();

  useEffect(() => {
    console.error(
      "404 Error: User attempted to access non-existent route:",
      location.pathname
    );
  }, [location.pathname]);

  const handleGoBack = () => {
    navigate(-1);
  };

  const handleGoHome = () => {
    navigate("/dashboard");
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-amber-50 to-orange-100 p-4">
      <Card className="w-full max-w-md shadow-xl border-0 animate-in slide-in-from-bottom-4 duration-500">
        <CardHeader className="text-center pb-4">
          <div className="w-20 h-20 rounded-full bg-amber-100 flex items-center justify-center mx-auto mb-4">
            <span className="text-6xl">🔍</span>
          </div>
          <CardTitle className="text-3xl font-bold text-gray-800 mb-2">
            404
          </CardTitle>
          <p className="text-xl text-gray-600">Page Not Found</p>
        </CardHeader>

        <CardContent className="space-y-6 text-center">
          <div className="space-y-2">
            <p className="text-gray-600">
              The page you're looking for doesn't exist or has been moved.
            </p>
            <p className="text-sm text-gray-500 font-mono bg-gray-100 p-2 rounded">
              {location.pathname}
            </p>
          </div>

          <div className="space-y-3">
            <Button
              onClick={handleGoHome}
              className="w-full bg-amber-500 hover:bg-amber-600 transition-all duration-200"
              size="lg"
            >
              <Home className="mr-2 h-4 w-4" />
              Go to Dashboard
            </Button>

            <Button
              onClick={handleGoBack}
              variant="outline"
              className="w-full border-amber-200 hover:bg-amber-50"
              size="lg"
            >
              <ArrowLeft className="mr-2 h-4 w-4" />
              Go Back
            </Button>
          </div>

          <div className="pt-4 border-t border-gray-200">
            <p className="text-sm text-gray-500 mb-3">
              Need help? Here are some suggestions:
            </p>
            <div className="space-y-2 text-sm">
              <Link
                to="/dashboard"
                className="flex items-center justify-center text-amber-600 hover:text-amber-700 transition-colors"
              >
                <Home className="mr-1 h-3 w-3" />
                Dashboard
              </Link>
              <Link
                to="/settings"
                className="flex items-center justify-center text-amber-600 hover:text-amber-700 transition-colors"
              >
                <Search className="mr-1 h-3 w-3" />
                Settings
              </Link>
              <a
                href="mailto:<EMAIL>"
                className="flex items-center justify-center text-amber-600 hover:text-amber-700 transition-colors"
              >
                <HelpCircle className="mr-1 h-3 w-3" />
                Contact Support
              </a>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
};

export default React.memo(NotFound);
