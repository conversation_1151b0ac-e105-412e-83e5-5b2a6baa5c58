import { configureStore, combineReducers, Middleware } from '@reduxjs/toolkit';
import { createLogger } from 'redux-logger';
import authSlice from './slices/authSlice';
import companiesSlice from './slices/companiesSlice';
import syncLogsSlice from './slices/syncLogsSlice';
import settingsSlice from './slices/settingsSlice';
import uiSlice from './slices/uiSlice';
import { errorHandler, ErrorCode } from '@/lib/error-handler';

// Root reducer
const rootReducer = combineReducers({
  auth: authSlice,
  companies: companiesSlice,
  syncLogs: syncLogsSlice,
  settings: settingsSlice,
  ui: uiSlice,
});

// Error handling middleware
const errorMiddleware: Middleware = (store) => (next) => (action) => {
  try {
    return next(action);
  } catch (error) {
    errorHandler.handleError(error, {
      action: action.type,
      payload: action.payload,
      store: 'redux',
    });
    throw error;
  }
};



// Configure middleware
const getMiddleware = (getDefaultMiddleware: any) => {
  const middleware = getDefaultMiddleware({
    serializableCheck: {
      ignoredActions: [
        'persist/PERSIST',
        'persist/REHYDRATE',
        'auth/loginUser/fulfilled',
        'auth/completeXeroOAuth/fulfilled',
      ],
      ignoredPaths: ['auth.user.lastLogin', 'auth.companyInfo.createdAt'],
    },
    immutableCheck: {
      warnAfter: 128,
    },
  });

  // Add custom middleware
  middleware.push(errorMiddleware);

  if (process.env.NODE_ENV === 'development') {
    middleware.push(createLogger({
      collapsed: true,
      duration: true,
      timestamp: true,
      predicate: (getState, action) => {
        // Don't log certain noisy actions
        const ignoredActions = ['ui/setLoading', 'ui/clearNotification'];
        return !ignoredActions.includes(action.type);
      },
    }));
  }

  return middleware;
};

export const store = configureStore({
  reducer: rootReducer,
  middleware: getMiddleware,
  devTools: process.env.NODE_ENV !== 'production' && {
    name: 'Xero Assiduous Sync',
    trace: true,
    traceLimit: 25,
  },
  preloadedState: undefined,
  enhancers: (getDefaultEnhancers) => getDefaultEnhancers(),
});

// Infer types from store
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;
export type AppStore = typeof store;

// Create a type that includes async thunk support
export type AppThunk<ReturnType = void> = (
  dispatch: AppDispatch,
  getState: () => RootState
) => ReturnType;

// Export root reducer for testing
export { rootReducer };
