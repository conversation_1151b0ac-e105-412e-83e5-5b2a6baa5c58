// Auth-related TypeScript interfaces and types

export interface CompanyInfo {
  id: string;
  name: string;
  legalName: string;
  payrollCalendarID: string;
  addresses: Array<{
    addressType: string;
    addressLine1: string;
    city: string;
    region: string;
    postalCode: string;
    country: string;
  }>;
  phones: Array<{
    phoneType: string;
    phoneNumber: string;
  }>;
  externalLinks: Array<{
    linkType: string;
    url: string;
  }>;
  taxNumber: string;
  financialYearEndDay: number;
  financialYearEndMonth: number;
  salesTaxBasis: string;
  salesTaxPeriod: string;
  defaultSalesTax: string;
  defaultPurchasesTax: string;
  periodLockDate: string;
  endOfYearLockDate: string;
  createdDateUTC: string;
  timezone: string;
  organisationEntityType: string;
  shortCode: string;
  organisationType: string;
  version: string;
  baseCurrency: string;
  countryCode: string;
  isDemoCompany: boolean;
  organisationStatus: string;
  registrationNumber: string;
  employerIdentificationNumber: string;
  taxType: string;
  lineOfBusiness: string;
  buildingNumber: string;
  street: string;
  suburb: string;
  city: string;
  region: string;
  postalCode: string;
  country: string;
}

export interface User {
  id: string;
  email: string;
  name: string;
  createdAt: string;
}

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface RegisterCredentials {
  email: string;
  password: string;
  name: string;
}

export interface AuthResponse {
  success: boolean;
  token: string;
  user: User;
}

export interface AuthState {
  // User authentication
  isAuthenticated: boolean;
  user: User | null;
  token: string | null;

  // Xero integration
  isConnected: boolean;
  isLoading: boolean;
  error: string | null;
  oauthUrl: string | null;
  companyInfo: CompanyInfo | null;
  accessToken: string | null;
  refreshToken: string | null;
}

export interface OAuthResponse {
  success: boolean;
  companyInfo?: CompanyInfo;
  error?: string;
}
