// Enhanced authentication hook
import { useCallback, useEffect } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import { useAppSelector, useAppDispatch } from '@/store/hooks';
import { loginUser, logoutUser, refreshToken, completeXeroOAuth } from '@/store/actions/auth.actions';
import { isTokenExpired } from '@/lib/auth.utils';
import { errorHandler, ErrorCode } from '@/lib/error-handler';

export interface LoginCredentials {
  email: string;
  password: string;
  rememberMe?: boolean;
}

export interface UseAuthReturn {
  // State
  user: any;
  isAuthenticated: boolean;
  isLoading: boolean;
  isConnected: boolean;
  companyInfo: any;
  error: string | null;
  
  // Actions
  login: (credentials: LoginCredentials) => Promise<boolean>;
  logout: () => Promise<void>;
  refreshAuth: () => Promise<boolean>;
  connectXero: () => void;
  completeXeroConnection: (code: string, state?: string) => Promise<boolean>;
  
  // Utilities
  hasPermission: (permission: string) => boolean;
  isTokenValid: () => boolean;
  getAuthHeaders: () => Record<string, string>;
}

export function useAuth(): UseAuthReturn {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();

  const {
    user,
    isAuthenticated,
    isLoading,
    isConnected,
    companyInfo,
    error,
    token,
    refreshToken: refreshTokenValue,
  } = useAppSelector((state) => state.auth);

  // Login function
  const login = useCallback(async (credentials: LoginCredentials): Promise<boolean> => {
    try {
      const result = await dispatch(loginUser(credentials)).unwrap();
      
      if (result.success) {
        // Redirect to intended page or dashboard
        const from = (location.state as any)?.from?.pathname || '/dashboard';
        navigate(from, { replace: true });
        return true;
      }
      
      return false;
    } catch (error) {
      errorHandler.handleError(error, { context: 'login', credentials: { email: credentials.email } });
      return false;
    }
  }, [dispatch, navigate, location]);

  // Logout function
  const logout = useCallback(async (): Promise<void> => {
    try {
      await dispatch(logoutUser()).unwrap();
      navigate('/login', { replace: true });
    } catch (error) {
      errorHandler.handleError(error, { context: 'logout' });
      // Force logout even if API call fails
      navigate('/login', { replace: true });
    }
  }, [dispatch, navigate]);

  // Refresh authentication
  const refreshAuth = useCallback(async (): Promise<boolean> => {
    if (!refreshTokenValue || isTokenExpired(refreshTokenValue)) {
      return false;
    }

    try {
      await dispatch(refreshToken()).unwrap();
      return true;
    } catch (error) {
      errorHandler.handleError(error, { context: 'refresh_token' });
      return false;
    }
  }, [dispatch, refreshTokenValue]);

  // Connect to Xero
  const connectXero = useCallback(() => {
    try {
      // Redirect to Xero OAuth URL
      window.location.href = `${process.env.VITE_API_BASE_URL || 'http://localhost:9000/api/v1'}/xero/connect`;
    } catch (error) {
      errorHandler.handleError(error, { context: 'xero_connect' });
    }
  }, []);

  // Complete Xero OAuth connection
  const completeXeroConnection = useCallback(async (code: string, state?: string): Promise<boolean> => {
    try {
      const result = await dispatch(completeXeroOAuth({ code, state })).unwrap();
      return result.success;
    } catch (error) {
      errorHandler.handleError(error, { context: 'xero_oauth_complete', code, state });
      return false;
    }
  }, [dispatch]);

  // Check if user has specific permission
  const hasPermission = useCallback((permission: string): boolean => {
    if (!user || !user.permissions) return false;
    
    // Admin users have all permissions
    if (user.role === 'admin') return true;
    
    // Check specific permission
    return user.permissions.includes(permission);
  }, [user]);

  // Check if current token is valid
  const isTokenValid = useCallback((): boolean => {
    if (!token) return false;
    return !isTokenExpired(token);
  }, [token]);

  // Get authorization headers for API requests
  const getAuthHeaders = useCallback((): Record<string, string> => {
    const headers: Record<string, string> = {};
    
    if (token && isTokenValid()) {
      headers.Authorization = `Bearer ${token}`;
    }
    
    return headers;
  }, [token, isTokenValid]);

  // Auto-refresh token when it's about to expire
  useEffect(() => {
    if (!isAuthenticated || !token) return;

    const checkTokenExpiry = () => {
      try {
        // Parse token to get expiry time
        const payload = JSON.parse(atob(token.split('.')[1]));
        const expiryTime = payload.exp * 1000; // Convert to milliseconds
        const currentTime = Date.now();
        const timeUntilExpiry = expiryTime - currentTime;
        
        // Refresh token 5 minutes before expiry
        if (timeUntilExpiry < 5 * 60 * 1000 && timeUntilExpiry > 0) {
          refreshAuth();
        }
      } catch (error) {
        console.warn('Failed to parse token for expiry check:', error);
      }
    };

    // Check immediately
    checkTokenExpiry();

    // Set up interval to check every minute
    const interval = setInterval(checkTokenExpiry, 60 * 1000);

    return () => clearInterval(interval);
  }, [isAuthenticated, token, refreshAuth]);

  // Redirect to login if not authenticated and trying to access protected route
  useEffect(() => {
    const publicRoutes = ['/login', '/register', '/forgot-password', '/reset-password'];
    const isPublicRoute = publicRoutes.some(route => location.pathname.startsWith(route));
    
    if (!isAuthenticated && !isPublicRoute && !isLoading) {
      navigate('/login', { 
        state: { from: location },
        replace: true 
      });
    }
  }, [isAuthenticated, location, navigate, isLoading]);

  return {
    // State
    user,
    isAuthenticated,
    isLoading,
    isConnected,
    companyInfo,
    error,
    
    // Actions
    login,
    logout,
    refreshAuth,
    connectXero,
    completeXeroConnection,
    
    // Utilities
    hasPermission,
    isTokenValid,
    getAuthHeaders,
  };
}

// Hook for protecting routes
export function useRequireAuth() {
  const { isAuthenticated, isLoading } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      navigate('/login', {
        state: { from: location },
        replace: true,
      });
    }
  }, [isAuthenticated, isLoading, navigate, location]);

  return { isAuthenticated, isLoading };
}

// Hook for role-based access control
export function useRequireRole(requiredRole: string) {
  const { user, hasPermission } = useAuth();
  
  const hasRole = user?.role === requiredRole || user?.role === 'admin';
  const hasRequiredPermission = hasPermission(requiredRole);
  
  return {
    hasAccess: hasRole || hasRequiredPermission,
    user,
  };
}
