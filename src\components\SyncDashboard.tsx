import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { 
  RefreshCw, 
  Play, 
  CheckCircle, 
  AlertTriangle, 
  Clock,
  Database,
  FileText,
  TrendingUp,
  Receipt,
  Banknote,
  Building2,
  Tag,
  Paperclip
} from 'lucide-react';

interface ApiEndpoint {
  name: string;
  category: string;
  status: 'idle' | 'syncing' | 'success' | 'error';
  lastSync?: string;
  recordCount?: number;
  icon: React.ReactNode;
}

const SyncDashboard: React.FC = () => {
  const [syncProgress, setSyncProgress] = useState(0);
  const [isGlobalSync, setIsGlobalSync] = useState(false);

  const apiEndpoints: ApiEndpoint[] = [
    { name: 'Accounts', category: 'Core', status: 'success', lastSync: '2024-06-12 10:30', recordCount: 45, icon: <Database className="h-4 w-4" /> },
    { name: 'Bank Transactions', category: 'Banking', status: 'success', lastSync: '2024-06-12 10:25', recordCount: 128, icon: <Banknote className="h-4 w-4" /> },
    { name: 'Journals', category: 'Financial', status: 'success', lastSync: '2024-06-12 08:45', recordCount: 23, icon: <FileText className="h-4 w-4" /> },
    { name: 'Invoices', category: 'Documents', status: 'success', lastSync: '2024-06-12 10:15', recordCount: 89, icon: <FileText className="h-4 w-4" /> },
    { name: 'Payments', category: 'Financial', status: 'success', lastSync: '2024-06-12 10:00', recordCount: 45, icon: <Banknote className="h-4 w-4" /> },
    { name: 'Manual Journals', category: 'Financial', status: 'idle', icon: <FileText className="h-4 w-4" /> },
    { name: 'Credit Notes', category: 'Documents', status: 'success', lastSync: '2024-06-12 09:45', recordCount: 12, icon: <Receipt className="h-4 w-4" /> },
    { name: 'Tracking Categories', category: 'Core', status: 'success', lastSync: '2024-06-12 08:15', recordCount: 6, icon: <Tag className="h-4 w-4" /> },
    { name: 'Tax Rates', category: 'Tax', status: 'success', lastSync: '2024-06-12 08:00', recordCount: 8, icon: <Database className="h-4 w-4" /> },
    { name: 'Attachments', category: 'Documents', status: 'idle', icon: <Paperclip className="h-4 w-4" /> },
  ];

  const reports = [
    { name: 'Profit & Loss', status: 'success', lastSync: '2024-06-12 10:35', icon: <TrendingUp className="h-4 w-4" /> },
    { name: 'Balance Sheet', status: 'success', lastSync: '2024-06-12 10:35', icon: <FileText className="h-4 w-4" /> },
    { name: 'Trial Balance', status: 'success', lastSync: '2024-06-12 10:35', icon: <Database className="h-4 w-4" /> },
  ];

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertTriangle className="h-4 w-4 text-red-500" />;
      case 'syncing':
        return <RefreshCw className="h-4 w-4 text-blue-500 animate-spin" />;
      default:
        return <Clock className="h-4 w-4 text-muted-foreground" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'success':
        return <Badge variant="default" className="bg-green-100 text-green-800">Success</Badge>;
      case 'error':
        return <Badge variant="destructive">Error</Badge>;
      case 'syncing':
        return <Badge variant="secondary" className="bg-blue-100 text-blue-800">Syncing</Badge>;
      default:
        return <Badge variant="outline">Pending</Badge>;
    }
  };

  const groupedEndpoints = apiEndpoints.reduce((acc, endpoint) => {
    if (!acc[endpoint.category]) {
      acc[endpoint.category] = [];
    }
    acc[endpoint.category].push(endpoint);
    return acc;
  }, {} as Record<string, ApiEndpoint[]>);

  const handleGlobalSync = () => {
    setIsGlobalSync(true);
    setSyncProgress(0);
    
    // Simulate sync progress
    const interval = setInterval(() => {
      setSyncProgress(prev => {
        if (prev >= 100) {
          clearInterval(interval);
          setIsGlobalSync(false);
          return 100;
        }
        return prev + 5;
      });
    }, 200);
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Sync Dashboard</h2>
          <p className="text-muted-foreground">Monitor and manage API synchronization with Xero</p>
        </div>
        <Button onClick={handleGlobalSync} disabled={isGlobalSync} size="lg">
          <RefreshCw className={`h-4 w-4 mr-2 ${isGlobalSync ? 'animate-spin' : ''}`} />
          {isGlobalSync ? 'Syncing...' : 'Sync All'}
        </Button>
      </div>

      {isGlobalSync && (
        <Card>
          <CardContent className="p-6">
            <div className="space-y-2">
              <div className="flex justify-between text-sm">
                <span>Global Sync Progress</span>
                <span>{syncProgress}%</span>
              </div>
              <Progress value={syncProgress} className="w-full" />
            </div>
          </CardContent>
        </Card>
      )}

      <Tabs defaultValue="endpoints" className="space-y-4">
        <TabsList>
          <TabsTrigger value="endpoints">API Endpoints</TabsTrigger>
          <TabsTrigger value="reports">Reports</TabsTrigger>
        </TabsList>

        <TabsContent value="endpoints" className="space-y-4">
          {Object.entries(groupedEndpoints).map(([category, endpoints]) => (
            <Card key={category}>
              <CardHeader>
                <CardTitle className="text-lg">{category} APIs</CardTitle>
                <CardDescription>
                  {endpoints.length} endpoints in this category
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {endpoints.map((endpoint) => (
                    <div
                      key={endpoint.name}
                      className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                    >
                      <div className="flex items-start justify-between mb-2">
                        <div className="flex items-center gap-2">
                          {endpoint.icon}
                          <h4 className="font-medium">{endpoint.name}</h4>
                        </div>
                        {getStatusIcon(endpoint.status)}
                      </div>
                      <div className="space-y-2">
                        {getStatusBadge(endpoint.status)}
                        {endpoint.lastSync && (
                          <p className="text-xs text-muted-foreground">
                            Last sync: {endpoint.lastSync}
                          </p>
                        )}
                        {endpoint.recordCount && (
                          <p className="text-xs text-muted-foreground">
                            {endpoint.recordCount} records
                          </p>
                        )}
                        <Button size="sm" variant="outline" className="w-full">
                          <Play className="h-3 w-3 mr-1" />
                          Sync Now
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </TabsContent>

        <TabsContent value="reports" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Financial Reports</CardTitle>
              <CardDescription>
                Sync financial reports from Xero to Assiduous platform
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                {reports.map((report) => (
                  <div
                    key={report.name}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center gap-2">
                        {report.icon}
                        <h4 className="font-medium">{report.name}</h4>
                      </div>
                      {getStatusIcon(report.status)}
                    </div>
                    <div className="space-y-2">
                      {getStatusBadge(report.status)}
                      {report.lastSync && (
                        <p className="text-xs text-muted-foreground">
                          Last sync: {report.lastSync}
                        </p>
                      )}
                      <Button size="sm" variant="outline" className="w-full">
                        <Play className="h-3 w-3 mr-1" />
                        Generate Report
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default SyncDashboard;
