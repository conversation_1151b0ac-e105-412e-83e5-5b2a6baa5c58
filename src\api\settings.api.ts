// Settings API service functions

import { apiClient, handleApiError } from './axios.config';
import { SyncEntity } from '../store/types/settings.types';

// Settings API endpoints
export const settingsApi = {
  // Fetch sync settings
  fetchSyncSettings: async (): Promise<{
    entities: SyncEntity[];
    globalSettings: {
      autoSync: boolean;
      syncInterval: number;
      retryAttempts: number;
      batchSize: number;
    };
  }> => {
    try {
      const response = await apiClient.get('/settings/sync');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Sync a specific entity
  syncEntity: async (params: {
    entityType: string;
    organizationId?: string;
    options?: {
      fullSync?: boolean;
      batchSize?: number;
    };
  }): Promise<{
    success: boolean;
    syncId: string;
    message: string;
  }> => {
    try {
      const response = await apiClient.post('/sync/entity', {
        entityType: params.entityType,
        organizationId: params.organizationId,
        options: params.options,
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Sync all entities
  syncAllEntities: async (params?: {
    organizationId?: string;
    options?: {
      fullSync?: boolean;
      batchSize?: number;
    };
  }): Promise<{
    success: boolean;
    syncId: string;
    message: string;
  }> => {
    try {
      const response = await apiClient.post('/sync/all', {
        organizationId: params?.organizationId,
        options: params?.options,
      });
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Update entity settings
  updateEntitySettings: async (params: {
    entityType: string;
    settings: Partial<SyncEntity>;
  }): Promise<{
    success: boolean;
    entityType: string;
    updatedSettings: Partial<SyncEntity>;
  }> => {
    try {
      const response = await apiClient.put(`/settings/entities/${params.entityType}`, params.settings);
      return {
        success: true,
        entityType: params.entityType,
        updatedSettings: response.data,
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Update global sync settings
  updateGlobalSettings: async (settings: {
    autoSync?: boolean;
    syncInterval?: number;
    retryAttempts?: number;
    batchSize?: number;
  }): Promise<{
    success: boolean;
    updatedSettings: typeof settings;
  }> => {
    try {
      const response = await apiClient.put('/settings/global', settings);
      return {
        success: true,
        updatedSettings: response.data,
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get sync status for all entities
  getSyncStatus: async (organizationId?: string): Promise<{
    entities: Array<{
      entityType: string;
      lastSync: string;
      syncInProgress: boolean;
      nextScheduledSync?: string;
      status: 'success' | 'error' | 'pending';
    }>;
    globalStatus: {
      totalEntities: number;
      syncingEntities: number;
      lastGlobalSync: string;
    };
  }> => {
    try {
      const params = organizationId ? `?organizationId=${organizationId}` : '';
      const response = await apiClient.get(`/sync/status${params}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Cancel ongoing sync
  cancelSync: async (syncId: string): Promise<{
    success: boolean;
    message: string;
  }> => {
    try {
      const response = await apiClient.post(`/sync/${syncId}/cancel`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get available entity types
  getAvailableEntityTypes: async (): Promise<{
    entityTypes: Array<{
      type: string;
      displayName: string;
      description: string;
      supportsIncremental: boolean;
    }>;
  }> => {
    try {
      const response = await apiClient.get('/settings/entity-types');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },
};

export default settingsApi;
