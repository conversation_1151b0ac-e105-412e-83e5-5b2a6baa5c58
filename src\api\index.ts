// API services index - centralized export for all API services

export { default as apiClient, handleApiError, isNetworkError, isTimeoutError } from './axios.config';

// User Routes API
export { authApi } from './auth.api';

// Company Management Routes API
export { companiesApi } from './companies.api';
export type { CompanyQueryParams, CompanyStats } from './companies.api';

// Sync Logging Routes API
export { syncLogsApi } from './syncLogs.api';
export type { CompanySyncLogFilters, CompanySyncStats, DetailedSyncLog } from './syncLogs.api';

// Settings API
export { settingsApi } from './settings.api';

// Xero Integration Routes API
export { xeroApi } from './xero.api';
export type {
    XeroAuthUrlResponse,
    XeroCallbackResponse,
    XeroConnectionStatus,
    XeroTokenRefreshResponse
} from './xero.api';

// Sync Management Routes API
export { syncApi } from './sync.api';
export type {
    EntitySyncStatus,
    SyncStatusResponse,
    SyncTriggerRequest,
    SyncTriggerResponse,
    SyncHistoryItem,
    SyncHistoryResponse,
    SyncEntity as SyncEntityType,
    SyncHistoryFilters
} from './sync.api';

// Re-export types for convenience
export type { CompanyInfo, OAuthResponse } from '../store/types/auth.types';
export type { Organization } from '../store/types/companies.types';
export type { SyncLog, SyncLogFilters } from '../store/types/syncLogs.types';
export type { SyncEntity } from '../store/types/settings.types';
