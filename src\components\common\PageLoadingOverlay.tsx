// Page Loading Overlay Component
// Shows loading state over page content instead of blank page

import React from 'react';
import { cn } from '@/lib/utils';
import { Loader2 } from 'lucide-react';

export interface PageLoadingOverlayProps {
  /** Whether the overlay is visible */
  isLoading: boolean;
  /** Loading message to display */
  message?: string;
  /** Additional CSS classes */
  className?: string;
  /** Whether to blur the background content */
  blur?: boolean;
  /** Loading spinner size */
  size?: 'sm' | 'md' | 'lg';
  /** Overlay opacity */
  opacity?: 'light' | 'medium' | 'dark';
  /** Whether to show over the entire page or just content area */
  fullPage?: boolean;
}

/**
 * Page Loading Overlay Component
 * 
 * Displays a loading overlay on top of page content instead of showing a blank page.
 * Maintains the page structure and shows loading state contextually.
 * 
 * Features:
 * - Configurable opacity and blur effects
 * - Responsive loading spinner sizes
 * - Customizable loading messages
 * - Full page or content area overlay options
 * - Smooth fade in/out animations
 * 
 * @param props PageLoadingOverlayProps
 * @returns JSX.Element
 */
export const PageLoadingOverlay: React.FC<PageLoadingOverlayProps> = ({
  isLoading,
  message = 'Loading...',
  className,
  blur = true,
  size = 'md',
  opacity = 'medium',
  fullPage = false,
}) => {
  if (!isLoading) return null;

  const sizeClasses = {
    sm: 'h-6 w-6',
    md: 'h-8 w-8',
    lg: 'h-12 w-12',
  };

  const opacityClasses = {
    light: 'bg-white/70',
    medium: 'bg-white/80',
    dark: 'bg-white/90',
  };

  return (
    <div
      className={cn(
        // Base positioning and layout
        'absolute inset-0 z-50 flex items-center justify-center',
        
        // Background and blur effects
        opacityClasses[opacity],
        blur && 'backdrop-blur-sm',
        
        // Animation
        'animate-in fade-in-0 duration-200',
        
        // Full page vs content area
        fullPage ? 'fixed' : 'absolute',
        
        className
      )}
    >
      {/* Loading content container */}
      <div className="flex flex-col items-center justify-center gap-4 p-6 bg-white rounded-lg shadow-lg border border-gray-200">
        {/* Loading spinner */}
        <Loader2 
          className={cn(
            'animate-spin text-amber-600',
            sizeClasses[size]
          )} 
        />
        
        {/* Loading message */}
        {message && (
          <div className="text-center">
            <p className="text-sm font-medium text-gray-700 mb-1">
              {message}
            </p>
            <p className="text-xs text-gray-500">
              Please wait while we load your data...
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

/**
 * API Loading Overlay Component
 * Specialized overlay for API loading states with contextual messages
 */
export const ApiLoadingOverlay: React.FC<{
  isLoading: boolean;
  operation?: string;
  className?: string;
}> = ({ 
  isLoading, 
  operation = 'data',
  className 
}) => {
  return (
    <PageLoadingOverlay
      isLoading={isLoading}
      message={`Loading ${operation}...`}
      size="md"
      opacity="medium"
      blur={true}
      className={className}
    />
  );
};

/**
 * Organizations Loading Overlay Component
 * Specialized overlay for organizations/companies loading
 */
export const OrganizationsLoadingOverlay: React.FC<{
  isLoading: boolean;
  className?: string;
}> = ({ isLoading, className }) => {
  return (
    <PageLoadingOverlay
      isLoading={isLoading}
      message="Loading organizations..."
      size="md"
      opacity="medium"
      blur={true}
      className={className}
    />
  );
};

/**
 * Sync Loading Overlay Component
 * Specialized overlay for sync operations
 */
export const SyncLoadingOverlay: React.FC<{
  isLoading: boolean;
  operation?: 'logs' | 'settings' | 'sync';
  className?: string;
}> = ({ 
  isLoading, 
  operation = 'sync',
  className 
}) => {
  const messages = {
    logs: 'Loading sync logs...',
    settings: 'Loading sync settings...',
    sync: 'Syncing data...',
  };

  return (
    <PageLoadingOverlay
      isLoading={isLoading}
      message={messages[operation]}
      size="md"
      opacity="medium"
      blur={true}
      className={className}
    />
  );
};

/**
 * Hook for managing page loading states
 */
export const usePageLoading = (initialState = false) => {
  const [isLoading, setIsLoading] = React.useState(initialState);

  const startLoading = React.useCallback(() => setIsLoading(true), []);
  const stopLoading = React.useCallback(() => setIsLoading(false), []);
  const toggleLoading = React.useCallback(() => setIsLoading(prev => !prev), []);

  return {
    isLoading,
    startLoading,
    stopLoading,
    toggleLoading,
    setIsLoading,
  };
};

export default PageLoadingOverlay;
