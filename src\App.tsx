import React, { Suspense, useEffect } from "react";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as Sonner } from "@/components/ui/sonner";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { Provider } from "react-redux";
import { store } from "./store";
import { ErrorBoundary } from "./components/ErrorBoundary";
import { PageLoader } from "./components/common/LoadingSpinner";

import { preloadCriticalRoutes } from "./components/LazyComponents";
import PrivateRoute from "./components/PrivateRoute";
import PublicRoute from "./components/PublicRoute";
import AuthProvider from "./components/AuthProvider";
import NotFound from "./pages/NotFound";

// Lazy load components for better performance
import {
  LazyIntegration,
  LazyLogin,
  <PERSON><PERSON><PERSON>egister,
  LazyXeroCallback,
  LazySettings,
  LazyCompanies,
  LazySyncLogs,
} from "./components/LazyComponents";

// Enhanced QueryClient configuration
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime in v5)
      retry: (failureCount: number, error: unknown) => {
        // Don't retry on 4xx errors except 408, 429
        const axiosError = error as { response?: { status?: number } };
        if (
          axiosError?.response?.status &&
          axiosError.response.status >= 400 &&
          axiosError.response.status < 500
        ) {
          return (
            axiosError.response.status === 408 ||
            axiosError.response.status === 429
          );
        }
        return failureCount < 3;
      },
      retryDelay: (attemptIndex) => Math.min(1000 * 2 ** attemptIndex, 30000),
    },
    mutations: {
      retry: 1,
    },
  },
});

// App performance initialization
const initializeApp = () => {
  // Preload critical routes
  preloadCriticalRoutes();

  // App initialized
};

// Main App component with enhanced architecture
const App: React.FC = () => {
  useEffect(() => {
    initializeApp();

    // Cleanup on unmount
    return () => {
      // Cleanup logic if needed
    };
  }, []);

  return (
    <ErrorBoundary>
      <Provider store={store}>
        <QueryClientProvider client={queryClient}>
          <TooltipProvider>
            <Toaster />
            <Sonner />
            <BrowserRouter>
              <AuthProvider>
                <Suspense
                  fallback={<PageLoader text="Loading application..." />}
                >
                  <Routes>
                    {/* Root redirect */}
                    <Route
                      path="/"
                      element={<Navigate to="/dashboard" replace />}
                    />

                    {/* Public routes - only accessible when not authenticated */}
                    <Route
                      path="/login"
                      element={
                        <PublicRoute>
                          <Suspense
                            fallback={<PageLoader text="Loading login..." />}
                          >
                            <LazyLogin />
                          </Suspense>
                        </PublicRoute>
                      }
                    />
                    <Route
                      path="/register"
                      element={
                        <PublicRoute>
                          <Suspense
                            fallback={
                              <PageLoader text="Loading registration..." />
                            }
                          >
                            <LazyRegister />
                          </Suspense>
                        </PublicRoute>
                      }
                    />

                    {/* Xero OAuth callback route - accessible to authenticated users */}
                    <Route
                      path="/xero/callback"
                      element={
                        <PrivateRoute>
                          <Suspense
                            fallback={
                              <PageLoader text="Processing Xero connection..." />
                            }
                          >
                            <LazyXeroCallback />
                          </Suspense>
                        </PrivateRoute>
                      }
                    />

                    {/* Private routes - only accessible when authenticated */}
                    <Route
                      path="/dashboard"
                      element={
                        <PrivateRoute>
                          <Suspense
                            fallback={
                              <PageLoader text="Loading integration..." />
                            }
                          >
                            <LazyIntegration />
                          </Suspense>
                        </PrivateRoute>
                      }
                    />

                    <Route
                      path="/companies"
                      element={
                        <PrivateRoute>
                          <Suspense
                            fallback={
                              <PageLoader text="Loading companies..." />
                            }
                          >
                            <LazyCompanies />
                          </Suspense>
                        </PrivateRoute>
                      }
                    />

                    <Route
                      path="/sync-logs"
                      element={
                        <PrivateRoute>
                          <Suspense
                            fallback={
                              <PageLoader text="Loading sync logs..." />
                            }
                          >
                            <LazySyncLogs />
                          </Suspense>
                        </PrivateRoute>
                      }
                    />

                    <Route
                      path="/settings"
                      element={
                        <PrivateRoute>
                          <Suspense
                            fallback={<PageLoader text="Loading settings..." />}
                          >
                            <LazySettings />
                          </Suspense>
                        </PrivateRoute>
                      }
                    />

                    {/* 404 catch-all route */}
                    <Route path="*" element={<NotFound />} />
                  </Routes>
                </Suspense>
              </AuthProvider>
            </BrowserRouter>
          </TooltipProvider>
        </QueryClientProvider>
      </Provider>
    </ErrorBoundary>
  );
};

export default App;
