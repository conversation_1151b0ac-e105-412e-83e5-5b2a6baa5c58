// Companies API service functions

import { apiClient, handleApiError } from './axios.config';
import { Organization, CompaniesApiResponse } from '../store/types/companies.types';

/**
 * Company query parameters for filtering and pagination
 */
export interface CompanyQueryParams {
  name?: string;
  connectionStatus?: string;
  hasXeroConnection?: boolean;
  createdAfter?: string;
  createdBefore?: string;
  limit?: number;
  offset?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

/**
 * Company statistics summary
 */
export interface CompanyStats {
  total: number;
  connected: number;
  disconnected: number;
  pending: number;
  lastSyncAt?: string;
  syncInProgress: number;
}

// Companies API endpoints
export const companiesApi = {
  /**
   * Get companies with filtering & pagination
   * GET /api/v1/companies
   */
  fetchOrganizations: async (params?: CompanyQueryParams): Promise<Organization[]> => {
    try {
      const response = await apiClient.get<CompaniesApiResponse>('/companies', {
        params,
      });

      // Transform backend response to frontend format
      const organizations: Organization[] = response.data?.data.companies.map(company => ({
        id: company.Id,
        name: company.Name,
        shortCode: company.Name.split(' ').map(word => word[0]).join('').toUpperCase(), // Generate shortCode from name
        tenantId: company.XeroTenantId,
        organizationStatus: company.ConnectionStatus,
        createdDateUTC: company.CreatedAt,
        // Keep original backend fields for reference
        Id: company.Id,
        Name: company.Name,
        XeroTenantId: company.XeroTenantId,
        ConnectionStatus: company.ConnectionStatus,
        FinancialYearEnd: company.FinancialYearEnd,
        CreatedAt: company.CreatedAt,
        UpdatedAt: company.UpdatedAt,
        XeroTokenExpiry: company.XeroTokenExpiry,
      }));

      return organizations;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Get company statistics summary
   * GET /api/v1/companies/stats
   */
  getCompanyStats: async (): Promise<CompanyStats> => {
    try {
      const response = await apiClient.get<CompanyStats>('/companies/stats');
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Get single company by ID
   * GET /api/v1/companies/:id
   */
  fetchOrganizationDetails: async (organizationId: string): Promise<Organization> => {
    try {
      const response = await apiClient.get(`/companies/${organizationId}`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Disconnect company's Xero connection
   * POST /api/v1/companies/:id/disconnect
   */
  disconnectCompany: async (companyId: string): Promise<{ success: boolean; message: string }> => {
    try {
      const response = await apiClient.post<{ success: boolean; message: string }>(`/companies/${companyId}/disconnect`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  /**
   * Reconnect company's Xero connection
   * POST /api/v1/companies/:id/reconnect
   */
  reconnectCompany: async (companyId: string): Promise<{ authUrl: string; message?: string }> => {
    try {
      const response = await apiClient.post<{ authUrl: string; message?: string }>(`/companies/${companyId}/reconnect`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error as any));
    }
  },

  // Update organization settings
  updateOrganizationSettings: async (
    organizationId: string,
    settings: Partial<Organization>
  ): Promise<{
    success: boolean;
    organizationId: string;
    updatedSettings: Partial<Organization>;
  }> => {
    try {
      const response = await apiClient.put(`/companies/${organizationId}`, settings);
      return {
        success: true,
        organizationId,
        updatedSettings: response.data,
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Disconnect organization
  disconnectOrganization: async (organizationId: string): Promise<{ organizationId: string }> => {
    try {
      const response = await apiClient.delete(`/companies/${organizationId}/disconnect`);
      return { organizationId };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Refresh organization data
  refreshOrganizationData: async (organizationId: string): Promise<{
    organizationId: string;
    updates: Partial<Organization>;
  }> => {
    try {
      const response = await apiClient.post(`/companies/${organizationId}/refresh`);
      return {
        organizationId,
        updates: response.data,
      };
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Get organization sync status
  getOrganizationSyncStatus: async (organizationId: string): Promise<{
    lastSync: string;
    syncInProgress: boolean;
    nextScheduledSync?: string;
  }> => {
    try {
      const response = await apiClient.get(`/companies/${organizationId}/sync-status`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },

  // Trigger manual sync for organization
  triggerOrganizationSync: async (organizationId: string): Promise<{
    success: boolean;
    syncId: string;
  }> => {
    try {
      const response = await apiClient.post(`/companies/${organizationId}/sync`);
      return response.data;
    } catch (error) {
      throw new Error(handleApiError(error));
    }
  },
};

export default companiesApi;
