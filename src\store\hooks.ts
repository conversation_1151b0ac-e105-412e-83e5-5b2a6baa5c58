import { useDispatch, useSelector, TypedUseSelectorHook } from 'react-redux';
import { useCallback, useMemo } from 'react';
import type { RootState, AppDispatch } from './index';
import type {
    Selector,
    ParametricSelector,
    AsyncThunkState,
    ListState,
    FormState
} from '@/types/store.types';

// Enhanced typed hooks for Redux
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

// Memoized selector hook
export function useMemoizedSelector<T>(
    selector: Selector<T>,
    deps?: React.DependencyList
): T {
    return useAppSelector(
        useMemo(() => selector, deps || [])
    );
}

// Parametric selector hook
export function useParametricSelector<P, T>(
    selector: ParametricSelector<P, T>,
    params: P
): T {
    return useAppSelector(
        useCallback((state: RootState) => selector(state, params), [selector, params])
    );
}

// Async thunk state hook
export function useAsyncThunkState(
    selector: Selector<AsyncThunkState>
) {
    const state = useAppSelector(selector);

    return {
        ...state,
        isIdle: !state.loading && !state.error,
        isSuccess: !state.loading && !state.error && state.lastFetch !== null,
        isError: !state.loading && state.error !== null,
    };
}

// List state hook with utilities
export function useListState<T>(
    selector: Selector<ListState<T>>
) {
    const state = useAppSelector(selector);

    return {
        ...state,
        isEmpty: state.items.length === 0,
        hasItems: state.items.length > 0,
        isFirstPage: state.pagination.page === 1,
        isLastPage: !state.pagination.hasNext,
        totalItems: state.pagination.total,
    };
}

// Form state hook with utilities
export function useFormState<T>(
    selector: Selector<FormState<T>>
) {
    const state = useAppSelector(selector);

    return {
        ...state,
        hasErrors: Object.keys(state.errors).length > 0,
        canSubmit: state.isValid && !state.isSubmitting,
        hasChanges: state.isDirty,
        isFirstSubmit: state.submitCount === 0,
    };
}

// Auth state hook
export function useAuthState() {
    return useAppSelector((state) => ({
        user: state.auth.user,
        isAuthenticated: state.auth.isAuthenticated,
        isLoading: state.auth.isLoading,
        isConnected: state.auth.isConnected,
        companyInfo: state.auth.companyInfo,
        error: state.auth.error,
        hasUser: state.auth.user !== null,
        isAdmin: state.auth.user?.role === 'admin',
        permissions: state.auth.user?.permissions || [],
    }));
}

// UI state hook
export function useUIState() {
    return useAppSelector((state) => ({
        theme: state.ui.theme,
        sidebarOpen: state.ui.sidebarOpen,
        sidebarCollapsed: state.ui.sidebarCollapsed,
        notifications: state.ui.notifications,
        modals: state.ui.modals,
        loading: state.ui.loading,
        preferences: state.ui.preferences,
        features: state.ui.features,
    }));
}

// Loading state hook for specific keys
export function useLoadingState(key?: string) {
    return useAppSelector((state) => {
        if (key) {
            return state.ui.loading[key] || false;
        }
        return Object.values(state.ui.loading).some(Boolean);
    });
}

// Companies state hook
export function useCompaniesState() {
    return useListState((state) => state.companies);
}

// Sync logs state hook
export function useSyncLogsState() {
    return useListState((state) => state.syncLogs);
}

// Settings state hook
export function useSettingsState() {
    return useAppSelector((state) => ({
        ...state.settings,
        hasUnsavedChanges: state.settings.hasUnsavedChanges,
        canSave: state.settings.hasUnsavedChanges && !state.settings.saveLoading,
        hasErrors: Object.keys(state.settings.validationErrors).length > 0,
    }));
}

// Notification hook
export function useNotifications() {
    const notifications = useAppSelector((state) => state.ui.notifications);

    return {
        notifications,
        hasNotifications: notifications.length > 0,
        unreadCount: notifications.filter(n => !n.id.includes('read')).length,
    };
}

// Modal state hook
export function useModal(modalKey: string) {
    const isOpen = useAppSelector((state) => state.ui.modals[modalKey] || false);
    const dispatch = useAppDispatch();

    return {
        isOpen,
        open: useCallback(() => {
            dispatch({ type: 'ui/openModal', payload: modalKey });
        }, [dispatch, modalKey]),
        close: useCallback(() => {
            dispatch({ type: 'ui/closeModal', payload: modalKey });
        }, [dispatch, modalKey]),
        toggle: useCallback(() => {
            dispatch({ type: 'ui/toggleModal', payload: modalKey });
        }, [dispatch, modalKey]),
    };
}

// Permission hook
export function usePermissions() {
    const { user } = useAuthState();

    return {
        hasPermission: useCallback((permission: string) => {
            if (!user) return false;
            if (user.role === 'admin') return true;
            return user.permissions.includes(permission);
        }, [user]),

        hasRole: useCallback((role: string) => {
            if (!user) return false;
            return user.role === role || user.role === 'admin';
        }, [user]),

        hasAnyPermission: useCallback((permissions: string[]) => {
            if (!user) return false;
            if (user.role === 'admin') return true;
            return permissions.some(permission => user.permissions.includes(permission));
        }, [user]),

        hasAllPermissions: useCallback((permissions: string[]) => {
            if (!user) return false;
            if (user.role === 'admin') return true;
            return permissions.every(permission => user.permissions.includes(permission));
        }, [user]),
    };
}
