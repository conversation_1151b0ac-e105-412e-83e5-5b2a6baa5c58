import React, { useEffect } from "react";
import { useLocation } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import {
  checkAuthStatus,
  fetchUserProfile,
} from "@/store/actions/auth.actions";
import { isTokenExpired, clearAuthTokens } from "@/lib/auth.utils";
import { Loader2 } from "lucide-react";

interface AuthProviderProps {
  children: React.ReactNode;
}

const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const dispatch = useAppDispatch();
  const location = useLocation();
  const { isLoading } = useAppSelector((state) => state.auth);
  const [isInitialized, setIsInitialized] = React.useState(false);

  // Routes that should not show the global loading state
  // These routes have their own custom loading UI that should not be overridden
  const routesWithCustomLoading = ["/xero/callback"];

  useEffect(() => {
    const initializeAuth = async () => {
      const token = localStorage.getItem("auth_token");

      if (token) {
        // Check if token is expired before making API call
        if (isTokenExpired(token)) {
          clearAuthTokens();
        } else {
          // Token is valid, check auth status with server
          try {
            await (dispatch as any)(checkAuthStatus());

            // Also fetch user profile to ensure sidebar shows current user info
            try {
              await (dispatch as any)(fetchUserProfile());
            } catch (profileError) {
              console.warn(
                "Failed to fetch user profile on init:",
                profileError
              );
              // Don't clear tokens if only profile fetch fails
            }
          } catch (error) {
            console.error("Auth check failed:", error);
            // If auth check fails, clear tokens
            clearAuthTokens();
          }
        }
      }

      setIsInitialized(true);
    };

    initializeAuth();
  }, [dispatch]);

  // Check if current route should show custom loading instead of global loading
  const shouldShowGlobalLoading = !routesWithCustomLoading.includes(
    location.pathname
  );

  // Show loading spinner while checking authentication (but not on routes with custom loading)
  if (!isInitialized || (isLoading && shouldShowGlobalLoading)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-amber-50 to-orange-100">
        <div className="flex flex-col items-center space-y-4">
          <div className="w-16 h-16 bg-amber-500 rounded-full flex items-center justify-center">
            <Loader2 className="w-8 h-8 text-white animate-spin" />
          </div>
          <p className="text-gray-600 font-medium">Loading...</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
};

export default AuthProvider;
