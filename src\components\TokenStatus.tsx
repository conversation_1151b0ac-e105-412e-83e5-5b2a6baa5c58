import React, { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Clock, 
  CheckCircle, 
  XCircle, 
  RefreshCw,
  AlertTriangle 
} from 'lucide-react';
import { 
  isTokenExpired, 
  getTokenTimeRemaining, 
  formatTimeRemaining,
  getUserFromToken 
} from '@/lib/auth.utils';

const TokenStatus: React.FC = () => {
  const [tokenInfo, setTokenInfo] = useState<{
    exists: boolean;
    expired: boolean;
    timeRemaining: string;
    userInfo: { userId: string; email: string } | null;
  }>({
    exists: false,
    expired: true,
    timeRemaining: 'No token',
    userInfo: null
  });

  const updateTokenInfo = () => {
    const token = localStorage.getItem('auth_token');
    
    if (!token) {
      setTokenInfo({
        exists: false,
        expired: true,
        timeRemaining: 'No token',
        userInfo: null
      });
      return;
    }

    const expired = isTokenExpired(token);
    const timeRemaining = formatTimeRemaining(token);
    const userInfo = getUserFromToken(token);

    setTokenInfo({
      exists: true,
      expired,
      timeRemaining,
      userInfo
    });
  };

  useEffect(() => {
    updateTokenInfo();
    
    // Update every 30 seconds
    const interval = setInterval(updateTokenInfo, 30000);
    
    return () => clearInterval(interval);
  }, []);

  const getStatusColor = () => {
    if (!tokenInfo.exists) return 'bg-gray-500';
    if (tokenInfo.expired) return 'bg-red-500';
    
    const timeRemaining = getTokenTimeRemaining(localStorage.getItem('auth_token'));
    if (!timeRemaining) return 'bg-red-500';
    
    // Less than 1 hour remaining
    if (timeRemaining.totalSeconds < 3600) return 'bg-yellow-500';
    
    return 'bg-green-500';
  };

  const getStatusIcon = () => {
    if (!tokenInfo.exists) return <XCircle className="w-4 h-4" />;
    if (tokenInfo.expired) return <XCircle className="w-4 h-4" />;
    
    const timeRemaining = getTokenTimeRemaining(localStorage.getItem('auth_token'));
    if (!timeRemaining) return <XCircle className="w-4 h-4" />;
    
    // Less than 1 hour remaining
    if (timeRemaining.totalSeconds < 3600) return <AlertTriangle className="w-4 h-4" />;
    
    return <CheckCircle className="w-4 h-4" />;
  };

  const getStatusText = () => {
    if (!tokenInfo.exists) return 'No Token';
    if (tokenInfo.expired) return 'Expired';
    
    const timeRemaining = getTokenTimeRemaining(localStorage.getItem('auth_token'));
    if (!timeRemaining) return 'Expired';
    
    // Less than 1 hour remaining
    if (timeRemaining.totalSeconds < 3600) return 'Expires Soon';
    
    return 'Valid';
  };

  return (
    <Card className="w-full max-w-md">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-lg">
          <Clock className="w-5 h-5" />
          Authentication Status
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Status Badge */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Token Status:</span>
          <Badge 
            className={`${getStatusColor()} text-white flex items-center gap-1`}
          >
            {getStatusIcon()}
            {getStatusText()}
          </Badge>
        </div>

        {/* Time Remaining */}
        <div className="flex items-center justify-between">
          <span className="text-sm font-medium">Time Remaining:</span>
          <span className="text-sm text-gray-600">
            {tokenInfo.timeRemaining}
          </span>
        </div>

        {/* User Info */}
        {tokenInfo.userInfo && (
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">User ID:</span>
              <span className="text-sm text-gray-600 font-mono">
                {tokenInfo.userInfo.userId.substring(0, 8)}...
              </span>
            </div>
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Email:</span>
              <span className="text-sm text-gray-600">
                {tokenInfo.userInfo.email}
              </span>
            </div>
          </div>
        )}

        {/* Refresh Button */}
        <Button
          onClick={updateTokenInfo}
          variant="outline"
          size="sm"
          className="w-full"
        >
          <RefreshCw className="w-4 h-4 mr-2" />
          Refresh Status
        </Button>

        {/* Debug Info */}
        <div className="text-xs text-gray-500 bg-gray-50 p-2 rounded">
          <div>Token exists: {tokenInfo.exists ? 'Yes' : 'No'}</div>
          <div>Is expired: {tokenInfo.expired ? 'Yes' : 'No'}</div>
          {tokenInfo.exists && (
            <div>
              Raw time: {getTokenTimeRemaining(localStorage.getItem('auth_token'))?.totalSeconds || 0}s
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
};

export default TokenStatus;
