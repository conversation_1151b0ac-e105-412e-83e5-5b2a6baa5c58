import React from 'react';
import { Navigate, useLocation } from 'react-router-dom';
import { useAppSelector } from '@/store/hooks';

interface PublicRouteProps {
  children: React.ReactNode;
}

const PublicRoute: React.FC<PublicRouteProps> = ({ children }) => {
  const { isAuthenticated, token } = useAppSelector((state) => state.auth);
  const location = useLocation();

  // Check if user is authenticated (either from Redux state or localStorage)
  const isUserAuthenticated = isAuthenticated || !!token || !!localStorage.getItem('auth_token');

  if (isUserAuthenticated) {
    // Get the intended destination from location state, or default to home
    const from = (location.state as any)?.from?.pathname || '/';
    return <Navigate to={from} replace />;
  }

  return <>{children}</>;
};

export default PublicRoute;
