// Redux store type definitions
import { store } from '@/store';
import { 
  User, 
  Company, 
  XeroConnection, 
  SyncLog, 
  AppSettings,
  Notification 
} from './api.types';

// Root state type
export type RootState = ReturnType<typeof store.getState>;
export type AppDispatch = typeof store.dispatch;

// Auth state types
export interface AuthState {
  // User data
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  
  // Tokens
  token: string | null;
  refreshToken: string | null;
  tokenExpiresAt: string | null;
  
  // Xero connection
  isConnected: boolean;
  xeroConnection: XeroConnection | null;
  companyInfo: Company | null;
  
  // Error handling
  error: string | null;
  lastError: {
    message: string;
    code?: string;
    timestamp: string;
  } | null;
  
  // Loading states
  loginLoading: boolean;
  logoutLoading: boolean;
  refreshLoading: boolean;
  xeroConnectLoading: boolean;
  
  // Session info
  lastActivity: string | null;
  sessionTimeout: number; // in minutes
}

// Companies state types
export interface CompaniesState {
  // Data
  companies: Company[];
  selectedCompany: Company | null;
  
  // Loading states
  loading: boolean;
  createLoading: boolean;
  updateLoading: boolean;
  deleteLoading: boolean;
  
  // Pagination
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  
  // Filters and search
  filters: {
    search: string;
    status: 'all' | 'active' | 'inactive';
    hasXeroConnection: boolean | null;
    industry: string | null;
    size: string | null;
  };
  
  // Sorting
  sort: {
    field: keyof Company;
    order: 'asc' | 'desc';
  };
  
  // Error handling
  error: string | null;
  lastFetch: string | null;
}

// Sync logs state types
export interface SyncLogsState {
  // Data
  logs: SyncLog[];
  selectedLog: SyncLog | null;
  
  // Loading states
  loading: boolean;
  syncLoading: boolean;
  cancelLoading: boolean;
  
  // Pagination
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  
  // Filters
  filters: {
    companyId: string | null;
    entityType: string | null;
    status: SyncLog['status'] | 'all';
    operation: SyncLog['operation'] | 'all';
    dateRange: {
      start: string | null;
      end: string | null;
    };
  };
  
  // Sorting
  sort: {
    field: keyof SyncLog;
    order: 'asc' | 'desc';
  };
  
  // Real-time updates
  activeSync: {
    logId: string;
    progress: number;
    status: string;
    recordsProcessed: number;
  } | null;
  
  // Statistics
  stats: {
    totalSyncs: number;
    successfulSyncs: number;
    failedSyncs: number;
    successRate: number;
    averageDuration: number;
    lastSyncAt: string | null;
  };
  
  // Error handling
  error: string | null;
  lastFetch: string | null;
}

// Settings state types
export interface SettingsState {
  // Data
  settings: Record<string, AppSettings>;
  
  // Loading states
  loading: boolean;
  saveLoading: boolean;
  resetLoading: boolean;
  
  // Categories
  categories: {
    general: AppSettings[];
    sync: AppSettings[];
    notifications: AppSettings[];
    security: AppSettings[];
    integration: AppSettings[];
  };
  
  // Dirty state tracking
  dirtySettings: Set<string>;
  hasUnsavedChanges: boolean;
  
  // Validation
  validationErrors: Record<string, string[]>;
  
  // Error handling
  error: string | null;
  lastSave: string | null;
}

// UI state types (from uiSlice)
export interface UIState {
  // Loading states for different operations
  loading: Record<string, boolean>;
  
  // Global notifications
  notifications: Array<{
    id: string;
    type: 'success' | 'error' | 'warning' | 'info';
    title: string;
    message: string;
    duration?: number;
    timestamp: number;
  }>;
  
  // Modal states
  modals: Record<string, boolean>;
  
  // Sidebar state
  sidebarOpen: boolean;
  sidebarCollapsed: boolean;
  
  // Theme and appearance
  theme: 'light' | 'dark' | 'system';
  
  // Layout preferences
  layout: {
    compactMode: boolean;
    showSidebar: boolean;
    sidebarWidth: number;
  };
  
  // Feature flags
  features: Record<string, boolean>;
  
  // User preferences
  preferences: {
    language: string;
    timezone: string;
    dateFormat: string;
    currency: string;
  };
}

// Async thunk states
export interface AsyncThunkState {
  loading: boolean;
  error: string | null;
  lastFetch: string | null;
}

// Generic list state
export interface ListState<T> {
  items: T[];
  selectedItem: T | null;
  loading: boolean;
  error: string | null;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
    hasNext: boolean;
    hasPrev: boolean;
  };
  filters: Record<string, any>;
  sort: {
    field: string;
    order: 'asc' | 'desc';
  };
  lastFetch: string | null;
}

// Form state types
export interface FormState<T = Record<string, any>> {
  data: T;
  originalData: T;
  isDirty: boolean;
  isValid: boolean;
  isSubmitting: boolean;
  errors: Record<keyof T, string[]>;
  touched: Record<keyof T, boolean>;
  submitCount: number;
  lastSubmit: string | null;
}

// API cache state
export interface ApiCacheState {
  cache: Record<string, {
    data: any;
    timestamp: number;
    ttl: number;
  }>;
  size: number;
  maxSize: number;
  hits: number;
  misses: number;
}

// WebSocket state
export interface WebSocketState {
  connected: boolean;
  connecting: boolean;
  error: string | null;
  lastMessage: any;
  subscriptions: string[];
  reconnectAttempts: number;
  maxReconnectAttempts: number;
}

// Performance state
export interface PerformanceState {
  measurements: Record<string, {
    count: number;
    min: number;
    max: number;
    avg: number;
    total: number;
  }>;
  longTasks: Array<{
    duration: number;
    timestamp: number;
    name?: string;
  }>;
  memoryUsage: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
    timestamp: number;
  } | null;
}

// Type utilities for Redux
export type AsyncThunkConfig = {
  state: RootState;
  dispatch: AppDispatch;
  rejectValue: string;
};

// Selector types
export type Selector<T> = (state: RootState) => T;
export type ParametricSelector<P, T> = (state: RootState, params: P) => T;

// Action payload types
export interface PaginationPayload {
  page?: number;
  limit?: number;
}

export interface SortPayload {
  field: string;
  order: 'asc' | 'desc';
}

export interface FilterPayload {
  [key: string]: any;
}

export interface SearchPayload {
  query: string;
  fields?: string[];
}

// Middleware types
export interface MiddlewareConfig {
  enableLogging: boolean;
  enablePerformanceTracking: boolean;
  enableErrorTracking: boolean;
  enableAnalytics: boolean;
}

// Store enhancer types
export interface StoreEnhancerConfig {
  devTools: boolean;
  preloadedState?: Partial<RootState>;
  middleware?: any[];
}

// Type guards for state
export function isLoadingState(state: any): state is AsyncThunkState {
  return typeof state === 'object' && 
         'loading' in state && 
         'error' in state;
}

export function isListState<T>(state: any): state is ListState<T> {
  return typeof state === 'object' && 
         'items' in state && 
         'pagination' in state;
}

export function isFormState<T>(state: any): state is FormState<T> {
  return typeof state === 'object' && 
         'data' in state && 
         'isDirty' in state && 
         'isValid' in state;
}
