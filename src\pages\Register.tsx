import React, { useState, use<PERSON><PERSON>back, useMemo } from "react";
import { useNavigate, Link } from "react-router-dom";
import { useAppDispatch, useAppSelector } from "@/store/hooks";
import { registerUser } from "@/store/actions/auth.actions";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Progress } from "@/components/ui/progress";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Loader2,
  Mail,
  Lock,
  User,
  UserPlus,
  Eye,
  EyeOff,
  CheckCircle,
  Circle,
  AlertCircle,
  Shield,
} from "lucide-react";
import { toast } from "@/components/ui/sonner";
import {
  validateEmail,
  validatePassword,
  validateName,
  checkPasswordStrength,
} from "@/lib/validation";

interface RegisterFormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: boolean;
  acceptMarketing: boolean;
}

interface FormErrors {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  confirmPassword: string;
  acceptTerms: string;
  general: string;
}

const Register: React.FC = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const { isLoading, error } = useAppSelector((state) => state.auth);

  const [formData, setFormData] = useState<RegisterFormData>({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    acceptTerms: false,
    acceptMarketing: false,
  });

  const [formErrors, setFormErrors] = useState<FormErrors>({
    firstName: "",
    lastName: "",
    email: "",
    password: "",
    confirmPassword: "",
    acceptTerms: "",
    general: "",
  });

  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  // Enhanced form validation with comprehensive checks
  const validateForm = useCallback((): boolean => {
    const errors: FormErrors = {
      firstName: "",
      lastName: "",
      email: "",
      password: "",
      confirmPassword: "",
      acceptTerms: "",
      general: "",
    };

    // Name validations
    const firstNameValidation = validateName(formData.firstName);
    if (!firstNameValidation.isValid) {
      errors.firstName = firstNameValidation.error || "First name is required";
    }

    const lastNameValidation = validateName(formData.lastName);
    if (!lastNameValidation.isValid) {
      errors.lastName = lastNameValidation.error || "Last name is required";
    }

    // Email validation
    const emailValidation = validateEmail(formData.email);
    if (!emailValidation.isValid) {
      errors.email = emailValidation.error || "Invalid email format";
    }

    // Password validation
    const passwordValidation = validatePassword(formData.password);
    if (!passwordValidation.isValid) {
      errors.password = passwordValidation.error || "Password is too weak";
    }

    // Confirm password validation
    if (!formData.confirmPassword) {
      errors.confirmPassword = "Please confirm your password";
    } else if (formData.password !== formData.confirmPassword) {
      errors.confirmPassword = "Passwords do not match";
    }

    // Terms acceptance validation
    if (!formData.acceptTerms) {
      errors.acceptTerms = "You must accept the terms and conditions";
    }

    setFormErrors(errors);
    return Object.values(errors).every((error) => !error);
  }, [formData]);

  // Real-time field validation
  const fieldValidation = useMemo(() => {
    return {
      firstName: {
        isValid: !formErrors.firstName && formData.firstName.length > 0,
        isEmpty: formData.firstName.length === 0,
      },
      lastName: {
        isValid: !formErrors.lastName && formData.lastName.length > 0,
        isEmpty: formData.lastName.length === 0,
      },
      email: {
        isValid: !formErrors.email && formData.email.length > 0,
        isEmpty: formData.email.length === 0,
      },
      password: {
        isValid: !formErrors.password && formData.password.length > 0,
        isEmpty: formData.password.length === 0,
      },
      confirmPassword: {
        isValid:
          !formErrors.confirmPassword &&
          formData.confirmPassword.length > 0 &&
          formData.password === formData.confirmPassword,
        isEmpty: formData.confirmPassword.length === 0,
      },
    };
  }, [formErrors, formData]);

  // Enhanced input change handler
  const handleInputChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement>) => {
      const { name, value, type, checked } = e.target;

      setFormData((prev) => ({
        ...prev,
        [name]: type === "checkbox" ? checked : value,
      }));

      // Clear specific field error when user starts typing
      if (formErrors[name as keyof FormErrors]) {
        setFormErrors((prev) => ({
          ...prev,
          [name]: "",
          general: "",
        }));
      }
    },
    [formErrors]
  );

  // Enhanced form submission
  const handleSubmit = useCallback(
    async (e: React.FormEvent) => {
      e.preventDefault();

      if (!validateForm()) {
        toast.error("Please fix the form errors", {
          description: "Check all required fields and try again.",
          duration: 3000,
        });
        return;
      }

      try {
        const registerData = {
          name: `${formData.firstName.trim()} ${formData.lastName.trim()}`,
          email: formData.email.toLowerCase().trim(),
          password: formData.password,
        };

        // Type assertion to work around Redux typing issue
        await (dispatch as any)(registerUser(registerData)).unwrap();

        toast.success("Account created successfully!", {
          description: "Welcome! You can now sign in to your account.",
          duration: 4000,
        });

        // Navigate to login page
        navigate("/login");
      } catch (error: any) {
        console.error("Registration failed:", error);

        let errorMessage = "Registration failed. Please try again.";

        if (error?.message?.includes("Email already exists")) {
          errorMessage =
            "An account with this email already exists. Please sign in instead.";
        } else if (error?.message?.includes("Invalid email")) {
          errorMessage = "Please enter a valid email address.";
        } else if (error?.message?.includes("Password too weak")) {
          errorMessage = "Please choose a stronger password.";
        }

        setFormErrors((prev) => ({
          ...prev,
          general: typeof error === "string" ? error : errorMessage,
        }));

        toast.error("Registration failed", {
          description: errorMessage,
          duration: 4000,
        });
      }
    },
    [formData, validateForm, dispatch, navigate]
  );

  // Enhanced password strength calculation
  const passwordStrengthData = useMemo(() => {
    return checkPasswordStrength(formData.password);
  }, [formData.password]);

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-lg shadow-xl border-0 animate-in slide-in-from-bottom-4 duration-500">
        <CardHeader className="space-y-1">
          <div className="flex items-center justify-center mb-4">
            <div className="w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center shadow-lg">
              <UserPlus className="w-6 h-6 text-white" />
            </div>
          </div>
          <CardTitle className="text-2xl font-bold text-center">
            Create Account
          </CardTitle>
          <CardDescription className="text-center">
            Join us today and start managing your Xero integrations
          </CardDescription>
        </CardHeader>

        <form onSubmit={handleSubmit}>
          <CardContent className="space-y-4">
            {/* General Error Alert */}
            {(error || formErrors.general) && (
              <Alert
                variant="destructive"
                className="animate-in slide-in-from-top-2"
              >
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  {formErrors.general || error}
                </AlertDescription>
              </Alert>
            )}

            {/* Name Fields Row */}
            <div className="grid grid-cols-2 gap-4">
              {/* First Name */}
              <div className="space-y-2">
                <Label htmlFor="firstName" className="text-sm font-medium">
                  First Name
                </Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="firstName"
                    name="firstName"
                    type="text"
                    placeholder="John"
                    value={formData.firstName}
                    onChange={handleInputChange}
                    className={`pl-10 transition-all duration-200 ${
                      formErrors.firstName
                        ? "border-red-500 focus:ring-red-500"
                        : fieldValidation.firstName.isValid
                        ? "border-green-500 focus:ring-green-500"
                        : ""
                    }`}
                    disabled={isLoading}
                    autoComplete="given-name"
                    required
                  />
                  {fieldValidation.firstName.isValid && (
                    <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
                  )}
                </div>
                {formErrors.firstName && (
                  <p className="text-xs text-red-500 animate-in slide-in-from-top-1">
                    {formErrors.firstName}
                  </p>
                )}
              </div>

              {/* Last Name */}
              <div className="space-y-2">
                <Label htmlFor="lastName" className="text-sm font-medium">
                  Last Name
                </Label>
                <div className="relative">
                  <User className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                  <Input
                    id="lastName"
                    name="lastName"
                    type="text"
                    placeholder="Doe"
                    value={formData.lastName}
                    onChange={handleInputChange}
                    className={`pl-10 transition-all duration-200 ${
                      formErrors.lastName
                        ? "border-red-500 focus:ring-red-500"
                        : fieldValidation.lastName.isValid
                        ? "border-green-500 focus:ring-green-500"
                        : ""
                    }`}
                    disabled={isLoading}
                    autoComplete="family-name"
                    required
                  />
                  {fieldValidation.lastName.isValid && (
                    <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
                  )}
                </div>
                {formErrors.lastName && (
                  <p className="text-xs text-red-500 animate-in slide-in-from-top-1">
                    {formErrors.lastName}
                  </p>
                )}
              </div>
            </div>

            {/* Email Field */}
            <div className="space-y-2">
              <Label htmlFor="email" className="text-sm font-medium">
                Email Address
              </Label>
              <div className="relative">
                <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="email"
                  name="email"
                  type="email"
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange}
                  className={`pl-10 transition-all duration-200 ${
                    formErrors.email
                      ? "border-red-500 focus:ring-red-500"
                      : fieldValidation.email.isValid
                      ? "border-green-500 focus:ring-green-500"
                      : ""
                  }`}
                  disabled={isLoading}
                  autoComplete="email"
                  required
                />
                {fieldValidation.email.isValid && (
                  <CheckCircle className="absolute right-3 top-3 h-4 w-4 text-green-500" />
                )}
              </div>
              {formErrors.email && (
                <p className="text-sm text-red-500 animate-in slide-in-from-top-1">
                  {formErrors.email}
                </p>
              )}
            </div>

            {/* Password Field */}
            <div className="space-y-2">
              <Label htmlFor="password" className="text-sm font-medium">
                Password
              </Label>
              <div className="relative">
                <Lock className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="password"
                  name="password"
                  type={showPassword ? "text" : "password"}
                  placeholder="Create a strong password"
                  value={formData.password}
                  onChange={handleInputChange}
                  className={`pl-10 pr-10 transition-all duration-200 ${
                    formErrors.password
                      ? "border-red-500 focus:ring-red-500"
                      : fieldValidation.password.isValid
                      ? "border-green-500 focus:ring-green-500"
                      : ""
                  }`}
                  disabled={isLoading}
                  autoComplete="new-password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowPassword(!showPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                  tabIndex={-1}
                >
                  {showPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
              </div>

              {/* Enhanced Password Strength Indicator */}
              {formData.password && (
                <div className="space-y-3">
                  {/* Strength Bar */}
                  <div className="space-y-1">
                    <Progress
                      value={passwordStrengthData.percentage}
                      className={`h-2 transition-all duration-300`}
                    />
                    <div className="flex justify-between text-xs">
                      <span
                        className={`font-medium transition-colors duration-300 ${
                          passwordStrengthData.level === "weak"
                            ? "text-red-600"
                            : passwordStrengthData.level === "fair"
                            ? "text-orange-600"
                            : passwordStrengthData.level === "good"
                            ? "text-yellow-600"
                            : passwordStrengthData.level === "strong"
                            ? "text-blue-600"
                            : "text-green-600"
                        }`}
                      >
                        {passwordStrengthData.description}
                      </span>
                      <span className="text-gray-500">
                        {Math.round(passwordStrengthData.percentage)}%
                      </span>
                    </div>
                  </div>

                  {/* Password Requirements Checklist */}
                  <div className="grid grid-cols-2 gap-1 text-xs">
                    <div
                      className={`flex items-center gap-1 transition-colors duration-200 ${
                        passwordStrengthData.checks.length
                          ? "text-green-600"
                          : "text-gray-400"
                      }`}
                    >
                      {passwordStrengthData.checks.length ? (
                        <CheckCircle className="h-3 w-3" />
                      ) : (
                        <Circle className="h-3 w-3" />
                      )}
                      <span>8+ characters</span>
                    </div>
                    <div
                      className={`flex items-center gap-1 transition-colors duration-200 ${
                        passwordStrengthData.checks.uppercase
                          ? "text-green-600"
                          : "text-gray-400"
                      }`}
                    >
                      {passwordStrengthData.checks.uppercase ? (
                        <CheckCircle className="h-3 w-3" />
                      ) : (
                        <Circle className="h-3 w-3" />
                      )}
                      <span>Uppercase</span>
                    </div>
                    <div
                      className={`flex items-center gap-1 transition-colors duration-200 ${
                        passwordStrengthData.checks.lowercase
                          ? "text-green-600"
                          : "text-gray-400"
                      }`}
                    >
                      {passwordStrengthData.checks.lowercase ? (
                        <CheckCircle className="h-3 w-3" />
                      ) : (
                        <Circle className="h-3 w-3" />
                      )}
                      <span>Lowercase</span>
                    </div>
                    <div
                      className={`flex items-center gap-1 transition-colors duration-200 ${
                        passwordStrengthData.checks.numbers
                          ? "text-green-600"
                          : "text-gray-400"
                      }`}
                    >
                      {passwordStrengthData.checks.numbers ? (
                        <CheckCircle className="h-3 w-3" />
                      ) : (
                        <Circle className="h-3 w-3" />
                      )}
                      <span>Numbers</span>
                    </div>
                    <div
                      className={`flex items-center gap-1 transition-colors duration-200 ${
                        passwordStrengthData.checks.symbols
                          ? "text-green-600"
                          : "text-gray-400"
                      }`}
                    >
                      {passwordStrengthData.checks.symbols ? (
                        <CheckCircle className="h-3 w-3" />
                      ) : (
                        <Circle className="h-3 w-3" />
                      )}
                      <span>Symbols</span>
                    </div>
                    <div
                      className={`flex items-center gap-1 transition-colors duration-200 ${
                        passwordStrengthData.checks.noRepeats
                          ? "text-green-600"
                          : "text-gray-400"
                      }`}
                    >
                      {passwordStrengthData.checks.noRepeats ? (
                        <CheckCircle className="h-3 w-3" />
                      ) : (
                        <Circle className="h-3 w-3" />
                      )}
                      <span>No repeats</span>
                    </div>
                  </div>

                  {/* Feedback Messages */}
                  {passwordStrengthData.feedback.length > 0 && (
                    <div className="text-xs text-amber-600 bg-amber-50 p-2 rounded border border-amber-200">
                      <div className="font-medium mb-1">Suggestions:</div>
                      <ul className="space-y-0.5">
                        {passwordStrengthData.feedback.map(
                          (feedback, index) => (
                            <li key={index} className="flex items-start gap-1">
                              <span className="text-amber-500 mt-0.5">•</span>
                              <span>{feedback}</span>
                            </li>
                          )
                        )}
                      </ul>
                    </div>
                  )}
                </div>
              )}

              {formErrors.password && (
                <p className="text-sm text-red-500 animate-in slide-in-from-top-1">
                  {formErrors.password}
                </p>
              )}
            </div>

            {/* Confirm Password Field */}
            <div className="space-y-2">
              <Label htmlFor="confirmPassword" className="text-sm font-medium">
                Confirm Password
              </Label>
              <div className="relative">
                <Shield className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                <Input
                  id="confirmPassword"
                  name="confirmPassword"
                  type={showConfirmPassword ? "text" : "password"}
                  placeholder="Confirm your password"
                  value={formData.confirmPassword}
                  onChange={handleInputChange}
                  className={`pl-10 pr-10 transition-all duration-200 ${
                    formErrors.confirmPassword
                      ? "border-red-500 focus:ring-red-500"
                      : fieldValidation.confirmPassword.isValid
                      ? "border-green-500 focus:ring-green-500"
                      : ""
                  }`}
                  disabled={isLoading}
                  autoComplete="new-password"
                  required
                />
                <button
                  type="button"
                  onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  className="absolute right-3 top-3 text-gray-400 hover:text-gray-600 transition-colors"
                  tabIndex={-1}
                >
                  {showConfirmPassword ? (
                    <EyeOff className="h-4 w-4" />
                  ) : (
                    <Eye className="h-4 w-4" />
                  )}
                </button>
                {fieldValidation.confirmPassword.isValid && (
                  <CheckCircle className="absolute right-10 top-3 h-4 w-4 text-green-500" />
                )}
              </div>
              {formErrors.confirmPassword && (
                <p className="text-sm text-red-500 animate-in slide-in-from-top-1">
                  {formErrors.confirmPassword}
                </p>
              )}
            </div>

            {/* Terms and Marketing Checkboxes */}
            <div className="space-y-3">
              <div className="flex items-start space-x-2">
                <Checkbox
                  id="acceptTerms"
                  name="acceptTerms"
                  checked={formData.acceptTerms}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({
                      ...prev,
                      acceptTerms: checked as boolean,
                    }))
                  }
                  disabled={isLoading}
                  className="mt-1"
                />
                <Label
                  htmlFor="acceptTerms"
                  className="text-sm text-gray-600 leading-relaxed"
                >
                  I agree to the Terms of Service and Privacy Policy
                </Label>
              </div>
              {formErrors.acceptTerms && (
                <p className="text-sm text-red-500 animate-in slide-in-from-top-1">
                  {formErrors.acceptTerms}
                </p>
              )}

              <div className="flex items-start space-x-2">
                <Checkbox
                  id="acceptMarketing"
                  name="acceptMarketing"
                  checked={formData.acceptMarketing}
                  onCheckedChange={(checked) =>
                    setFormData((prev) => ({
                      ...prev,
                      acceptMarketing: checked as boolean,
                    }))
                  }
                  disabled={isLoading}
                  className="mt-1"
                />
                <Label
                  htmlFor="acceptMarketing"
                  className="text-sm text-gray-600 leading-relaxed"
                >
                  I would like to receive product updates and marketing
                  communications
                </Label>
              </div>
            </div>
          </CardContent>

          <CardFooter className="flex flex-col space-y-4">
            <Button
              type="submit"
              className="w-full bg-blue-500 hover:bg-blue-600 transition-all duration-200 shadow-lg hover:shadow-xl"
              disabled={isLoading}
              size="lg"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Account...
                </>
              ) : (
                <>
                  <UserPlus className="mr-2 h-4 w-4" />
                  Create Account
                </>
              )}
            </Button>

            <div className="text-center text-sm text-gray-600">
              Already have an account?{" "}
              <Link
                to="/login"
                className="text-blue-600 hover:text-blue-700 font-medium transition-colors"
              >
                Sign in
              </Link>
            </div>
          </CardFooter>
        </form>
      </Card>
    </div>
  );
};

export default React.memo(Register);
