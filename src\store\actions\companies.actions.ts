// Companies-related async thunks and actions

import { createAsyncThunk } from '@reduxjs/toolkit';
import { Organization, FetchOrganizationsResponse } from '../types/companies.types';
import { companiesApi, CompanyQueryParams, CompanyStats } from '../../api/companies.api';
import type { RootState, AppDispatch } from '../index';

/**
 * Fetch organizations with optional filtering and pagination
 * GET /api/v1/companies
 */
export const fetchOrganizations = createAsyncThunk<
  Organization[],
  CompanyQueryParams | void,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'companies/fetchOrganizations',
  async (params, { rejectWithValue }) => {
    try {
      const organizations = await companiesApi.fetchOrganizations(params);
      return organizations;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch organizations');
    }
  }
);

/**
 * Get company statistics summary
 * GET /api/v1/companies/stats
 */
export const getCompanyStats = createAsyncThunk<
  CompanyStats,
  void,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'companies/getStats',
  async (_, { rejectWithValue }) => {
    try {
      const stats = await companiesApi.getCompanyStats();
      return stats;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to get company stats');
    }
  }
);

// Async thunk to fetch organization details
export const fetchOrganizationDetails = createAsyncThunk<
  Organization,
  string,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'companies/fetchOrganizationDetails',
  async (organizationId: string, { rejectWithValue }) => {
    try {
      const organization = await companiesApi.fetchOrganizationDetails(organizationId);
      return organization;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to fetch organization details');
    }
  }
);

// Async thunk to update organization settings
export const updateOrganizationSettings = createAsyncThunk<
  Organization,
  { organizationId: string; settings: Partial<Organization> },
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'companies/updateOrganizationSettings',
  async (params: { organizationId: string; settings: Partial<Organization> }, { rejectWithValue }) => {
    try {
      const response = await companiesApi.updateOrganizationSettings(params.organizationId, params.settings);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to update organization settings');
    }
  }
);

/**
 * Disconnect organization (legacy method)
 * @deprecated Use disconnectCompany instead
 */
export const disconnectOrganization = createAsyncThunk<
  { organizationId: string },
  string,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'companies/disconnectOrganization',
  async (organizationId: string, { rejectWithValue }) => {
    try {
      const response = await companiesApi.disconnectOrganization(organizationId);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to disconnect organization');
    }
  }
);

/**
 * Disconnect company's Xero connection
 * POST /api/v1/companies/:id/disconnect
 */
export const disconnectCompany = createAsyncThunk<
  { success: boolean; message: string },
  string, // companyId
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'companies/disconnectCompany',
  async (companyId: string, { rejectWithValue }) => {
    try {
      const response = await companiesApi.disconnectCompany(companyId);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to disconnect company');
    }
  }
);

/**
 * Reconnect company's Xero connection
 * POST /api/v1/companies/:id/reconnect
 */
export const reconnectCompany = createAsyncThunk<
  { authUrl: string; message?: string },
  string, // companyId
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'companies/reconnectCompany',
  async (companyId: string, { rejectWithValue }) => {
    try {
      const response = await companiesApi.reconnectCompany(companyId);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to reconnect company');
    }
  }
);

// Async thunk to refresh organization data
export const refreshOrganizationData = createAsyncThunk<
  { organizationId: string; updates: Partial<Organization> },
  string,
  {
    state: RootState;
    dispatch: AppDispatch;
    rejectValue: string;
  }
>(
  'companies/refreshOrganizationData',
  async (organizationId: string, { rejectWithValue }) => {
    try {
      const response = await companiesApi.refreshOrganizationData(organizationId);
      return response;
    } catch (error) {
      return rejectWithValue(error instanceof Error ? error.message : 'Failed to refresh organization data');
    }
  }
);
